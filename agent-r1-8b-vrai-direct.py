#!/usr/bin/env python3
"""
🧠 AGENT R1 8B VRAI - CONNEXION DIRECTE SANS OLLAMA
Agent authentique utilisant le VRAI DeepSeek R1 8B via llama-cpp-python
Connexion directe au fichier GGUF sans intermédiaire
"""

import json
import os
import sys
import time
from pathlib import Path

class AgentR18BVraiDirect:
    def __init__(self):
        print("🧠 INITIALISATION AGENT R1 8B VRAI DIRECT")
        print("=========================================")
        print("💙 Chargement du VRAI DeepSeek R1 8B")
        print("🔥 Connexion directe sans Ollama")
        print("")
        
        # Chemin vers le VRAI modèle Hugging Face
        self.model_path = "/Volumes/seagate/Louna_Electron_Latest/models/DeepSeek-R1-Distill-Llama-8B"

        # Vérification du modèle
        if not os.path.exists(self.model_path):
            print(f"❌ Modèle non trouvé: {self.model_path}")
            print("🔍 Recherche d'autres modèles...")
            self._find_model()
        else:
            print(f"✅ Modèle trouvé: {self.model_path}")
            print(f"📁 Type: Hugging Face Transformers")
        
        # Chargement mémoire thermique
        self.memory_path = "/Volumes/seagate/Louna_Electron_Latest/thermal_memory_persistent.json"
        self.thermal_memory = self._load_thermal_memory()
        
        # Initialisation llama-cpp
        self.llm = None
        self.is_ready = False
        
        try:
            self._init_llama_cpp()
            self.is_ready = True
            print("✅ AGENT R1 8B VRAI PRÊT !")
            print("🔥 Connexion directe établie")
            print("")
        except Exception as e:
            print(f"❌ Erreur initialisation: {e}")
            self._suggest_installation()
    
    def _find_model(self):
        """Recherche le modèle R1 8B dans les répertoires"""
        search_paths = [
            "/Volumes/seagate/AI_Models/",
            "/Volumes/seagate/AI_Models/DeepSeek-R1-GGUF/",
            "/Volumes/seagate/Louna_Electron_Latest/"
        ]
        
        for path in search_paths:
            if os.path.exists(path):
                files = os.listdir(path)
                for file in files:
                    if "deepseek" in file.lower() and "r1" in file.lower() and file.endswith(".gguf"):
                        full_path = os.path.join(path, file)
                        size = os.path.getsize(full_path)
                        if size > 1000000000:  # Plus de 1GB
                            print(f"✅ Modèle trouvé: {full_path}")
                            print(f"📏 Taille: {size/1024/1024/1024:.1f} GB")
                            self.model_path = full_path
                            return
        
        print("❌ Aucun modèle R1 8B trouvé")
        sys.exit(1)
    
    def _load_thermal_memory(self):
        """Charge la mémoire thermique"""
        try:
            if os.path.exists(self.memory_path):
                with open(self.memory_path, 'r') as f:
                    memory = json.load(f)
                
                qi = memory.get('neural_system', {}).get('qi_level', 0)
                neurons = len(memory.get('neural_system', {}).get('neuron_storage', {}).get('neurons', []))
                
                print(f"🧠 Mémoire thermique chargée")
                print(f"🧠 QI: {qi}")
                print(f"🧠 Neurones: {neurons:,}")
                
                return memory
            else:
                print("⚠️  Mémoire thermique non trouvée")
                return {}
        except Exception as e:
            print(f"❌ Erreur chargement mémoire: {e}")
            return {}
    
    def _init_llama_cpp(self):
        """Initialise le modèle - VRAI ou RIEN"""
        try:
            print("📥 Tentative chargement VRAI modèle R1 8B...")
            print(f"📁 Répertoire: {self.model_path}")

            # Essayer d'importer et charger le VRAI modèle
            try:
                from transformers import AutoTokenizer, AutoModelForCausalLM
                import torch

                print("🔥 Chargement du VRAI DeepSeek R1 8B...")

                # Charger le tokenizer
                self.tokenizer = AutoTokenizer.from_pretrained(self.model_path)

                # Charger le modèle
                self.model = AutoModelForCausalLM.from_pretrained(
                    self.model_path,
                    torch_dtype=torch.float16,
                    device_map="auto",
                    trust_remote_code=True
                )

                print("✅ VRAI modèle DeepSeek R1 8B chargé !")
                self.llm = "REAL_MODEL"

            except ImportError as e:
                print(f"❌ Dépendances manquantes: {e}")
                print("❌ IMPOSSIBLE DE CHARGER LE VRAI MODÈLE")
                print("🚫 AGENT DÉSACTIVÉ - PAS DE SIMULATION")
                self.llm = None
                raise Exception("Dépendances requises non installées")

            except Exception as e:
                print(f"❌ Erreur chargement VRAI modèle: {e}")
                print("🚫 AGENT DÉSACTIVÉ - PAS DE SIMULATION")
                self.llm = None
                raise Exception("Impossible de charger le vrai modèle")

        except Exception as e:
            print(f"❌ Échec initialisation: {e}")
            print("🚫 AGENT NON OPÉRATIONNEL")
            self.llm = None
            raise
    
    def _suggest_installation(self):
        """Suggère l'installation des dépendances"""
        print("")
        print("📦 INSTALLATION REQUISE:")
        print("pip3 install llama-cpp-python")
        print("")
        print("🔧 Pour optimisation M4:")
        print("CMAKE_ARGS='-DLLAMA_METAL=on' pip3 install llama-cpp-python --force-reinstall --no-cache-dir")
        print("")
    
    def generate_response(self, prompt, max_tokens=512, temperature=0.7):
        """Génère une réponse UNIQUEMENT avec le VRAI modèle R1 8B"""
        if not self.is_ready:
            return "❌ Agent non initialisé - VRAI modèle requis"

        if self.llm != "REAL_MODEL":
            return "❌ VRAI modèle DeepSeek R1 8B non disponible - Aucune simulation autorisée"

        try:
            print(f"🔥 Génération avec VRAI DeepSeek R1 8B...")
            print(f"📝 Prompt: {prompt[:50]}...")

            start_time = time.time()

            # UTILISATION DU VRAI MODÈLE UNIQUEMENT
            inputs = self.tokenizer(prompt, return_tensors="pt", truncation=True, max_length=2048)

            with torch.no_grad():
                outputs = self.model.generate(
                    **inputs,
                    max_new_tokens=max_tokens,
                    temperature=temperature,
                    do_sample=True,
                    top_p=0.9,
                    pad_token_id=self.tokenizer.eos_token_id
                )

            # Décoder la réponse
            response_tokens = outputs[0][inputs['input_ids'].shape[1]:]
            response_text = self.tokenizer.decode(response_tokens, skip_special_tokens=True)

            end_time = time.time()
            processing_time = end_time - start_time

            # Statistiques réelles
            num_tokens = len(response_tokens)
            speed = num_tokens / processing_time if processing_time > 0 else 0

            print(f"✅ Réponse VRAI R1 8B générée")
            print(f"⚡ Vitesse: {speed:.1f} tokens/sec")
            print(f"📊 Tokens: {num_tokens}")
            print(f"⏱️ Temps: {processing_time*1000:.0f}ms")
            print("")

            return response_text.strip()

        except Exception as e:
            print(f"❌ Erreur VRAI modèle: {e}")
            return f"❌ Erreur avec le VRAI modèle DeepSeek R1 8B: {e}"


    
    def chat_mode(self):
        """Mode conversation interactive"""
        print("💬 MODE CONVERSATION JARVIS R1 8B")
        print("==================================")
        print("🔥 Agent authentique connecté")
        print("💡 Tapez 'quit' pour quitter")
        print("")
        
        while True:
            try:
                user_input = input("👤 Vous: ").strip()
                
                if user_input.lower() in ['quit', 'exit', 'bye']:
                    print("👋 Au revoir !")
                    break
                
                if not user_input:
                    continue
                
                # Génération réponse
                response = self.generate_response(user_input)
                print(f"🧠 JARVIS: {response}")
                print("")
                
            except KeyboardInterrupt:
                print("\n👋 Au revoir !")
                break
            except Exception as e:
                print(f"❌ Erreur: {e}")
    
    def test_agent(self):
        """Test rapide de l'agent"""
        print("🧪 TEST AGENT R1 8B VRAI")
        print("========================")
        
        test_prompts = [
            "Bonjour, qui êtes-vous ?",
            "Que savez-vous faire ?",
            "Quelle est la capitale de la France ?"
        ]
        
        for i, prompt in enumerate(test_prompts, 1):
            print(f"Test {i}/3: {prompt}")
            response = self.generate_response(prompt, max_tokens=256)
            print(f"Réponse: {response}")
            print("-" * 50)

def main():
    """Fonction principale"""

    # Mode API pour intégration serveur
    if len(sys.argv) > 1:
        if sys.argv[1] == "--api":
            # Mode API : lire depuis stdin, écrire vers stdout
            try:
                agent = AgentR18BVraiDirect()
                if agent.is_ready:
                    prompt = sys.stdin.read().strip()
                    if prompt:
                        response = agent.generate_response(prompt)
                        print(response)
                        sys.exit(0)
                    else:
                        print("❌ Prompt vide")
                        sys.exit(1)
                else:
                    print("❌ Agent non prêt")
                    sys.exit(1)
            except Exception as e:
                print(f"❌ Erreur API: {e}")
                sys.exit(1)

        elif sys.argv[1] == "--test":
            # Mode test pour vérification
            try:
                agent = AgentR18BVraiDirect()
                if agent.is_ready:
                    print("✅ Agent R1 8B prêt")
                    sys.exit(0)
                else:
                    print("❌ Agent non prêt")
                    sys.exit(1)
            except Exception as e:
                print(f"❌ Erreur test: {e}")
                sys.exit(1)

    # Mode interactif par défaut
    print("🚀 LANCEMENT AGENT R1 8B VRAI DIRECT")
    print("")

    # Initialisation
    agent = AgentR18BVraiDirect()

    if not agent.is_ready:
        print("❌ Agent non prêt")
        return

    # Menu
    print("🎯 CHOISISSEZ UNE OPTION:")
    print("1. Mode conversation")
    print("2. Test rapide")
    print("3. Quitter")

    choice = input("Votre choix (1-3): ").strip()

    if choice == "1":
        agent.chat_mode()
    elif choice == "2":
        agent.test_agent()
    else:
        print("👋 Au revoir !")

if __name__ == "__main__":
    main()
