/**
 * TEST SIMPLE DU MOTEUR D'ÉVEIL
 */

const MemoryConsciousnessEngine = require('./memory_consciousness_engine.js');

async function simpleTest() {
    console.log('🧠 TEST SIMPLE D\'ÉVEIL DE LA MÉMOIRE THERMIQUE');
    console.log('='.repeat(50));
    
    try {
        // Initialiser le moteur avec le fichier de mémoire le plus complet
        const engine = new MemoryConsciousnessEngine('./thermal_memory_backup_1749871795600.json');
        
        // Charger la mémoire
        console.log('\n📖 Chargement de la mémoire thermique...');
        const loaded = await engine.loadThermalMemory();
        
        if (!loaded) {
            console.log('❌ Échec du chargement');
            return;
        }
        
        console.log('✅ Mémoire chargée avec succès');
        
        // Test d'introspection
        console.log('\n🔍 Test d\'introspection...');
        const introspection = engine.introspect();
        console.log(`✅ ${introspection.discoveries.length} découvertes`);
        
        // Test de raisonnement
        console.log('\n🧠 Test de raisonnement...');
        const reasoning = engine.reason();
        console.log(`✅ ${reasoning.inferences.length} inférences`);
        
        // Test d'évolution
        console.log('\n🚀 Test d\'évolution...');
        const evolution = engine.evolve();
        console.log(`✅ ${evolution.modifications.length} modifications`);
        
        // Niveau de conscience
        const level = engine.getConsciousnessLevel();
        console.log(`\n📊 Niveau de conscience: ${level.toFixed(2)}/10`);
        
        // Détection d'éveil
        const awakening = engine.detectAwakening();
        console.log(`🌟 Éveil détecté: ${awakening ? 'OUI' : 'NON'}`);
        
        console.log('\n✅ Test terminé avec succès !');
        
    } catch (error) {
        console.error('❌ Erreur:', error.message);
        console.error(error.stack);
    }
}

// Exécuter le test
simpleTest();
