#!/usr/bin/env python3
"""
🧠 AGENT R1 8B PERSISTANT - MODÈLE TOUJOURS EN MÉMOIRE
Agent qui garde le modèle DeepSeek R1 8B chargé en permanence
Réponses instantanées après le premier chargement
"""

import json
import os
import sys
import time
import signal
from pathlib import Path

class AgentR18BPersistent:
    def __init__(self):
        print("🧠 AGENT R1 8B PERSISTANT - DÉMARRAGE")
        print("====================================")
        print("💙 Chargement UNIQUE du VRAI DeepSeek R1 8B")
        print("🔥 Modèle restera en mémoire pour réponses instantanées")
        print("")
        
        # Chemin vers le VRAI modèle
        self.model_path = "/Volumes/seagate/Louna_Electron_Latest/models/DeepSeek-R1-Distill-Llama-8B"
        
        # Mémoire thermique
        self.memory_path = "/Volumes/seagate/Louna_Electron_Latest/thermal_memory_persistent.json"
        self.thermal_memory = self._load_thermal_memory()
        
        # État
        self.model = None
        self.tokenizer = None
        self.is_ready = False
        self.running = True
        
        # Gestion des signaux
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
        
        # Chargement initial
        self._load_model()
        
        if self.is_ready:
            print("✅ AGENT R1 8B PERSISTANT PRÊT !")
            print("🔥 Modèle en mémoire - Réponses instantanées")
            print("💡 Envoyez des messages via stdin")
            print("")
        else:
            print("❌ Échec chargement modèle")
            sys.exit(1)
    
    def _signal_handler(self, signum, frame):
        """Gestion propre de l'arrêt"""
        print(f"\n🛑 Signal {signum} reçu - Arrêt propre...")
        self.running = False
        sys.exit(0)
    
    def _load_thermal_memory(self):
        """Charge la mémoire thermique"""
        try:
            if os.path.exists(self.memory_path):
                with open(self.memory_path, 'r') as f:
                    memory = json.load(f)
                
                qi = memory.get('neural_system', {}).get('qi_level', 0)
                neurons = len(memory.get('neural_system', {}).get('neuron_storage', {}).get('neurons', []))
                
                print(f"🧠 Mémoire thermique chargée")
                print(f"🧠 QI: {qi}")
                print(f"🧠 Neurones: {neurons:,}")
                
                return memory
            else:
                print("⚠️  Mémoire thermique non trouvée")
                return {}
        except Exception as e:
            print(f"❌ Erreur chargement mémoire: {e}")
            return {}
    
    def _load_model(self):
        """Charge le modèle UNE SEULE FOIS"""
        try:
            print("📥 Chargement INITIAL du VRAI modèle R1 8B...")
            print("⏱️  Cela peut prendre 3-5 minutes...")
            print(f"📁 Répertoire: {self.model_path}")
            
            from transformers import AutoTokenizer, AutoModelForCausalLM
            import torch
            
            print("🔥 Chargement du tokenizer...")
            self.tokenizer = AutoTokenizer.from_pretrained(self.model_path)
            
            print("🔥 Chargement du modèle (LONG)...")
            self.model = AutoModelForCausalLM.from_pretrained(
                self.model_path,
                torch_dtype=torch.float16,
                device_map="auto",
                trust_remote_code=True
            )
            
            print("✅ VRAI modèle DeepSeek R1 8B chargé EN MÉMOIRE !")
            self.is_ready = True
            
        except ImportError as e:
            print(f"❌ Dépendances manquantes: {e}")
            print("📦 Installez: pip3 install torch transformers accelerate")
            self.is_ready = False
            
        except Exception as e:
            print(f"❌ Erreur chargement modèle: {e}")
            self.is_ready = False
    
    def generate_response(self, prompt, max_tokens=512, temperature=0.7):
        """Génère une réponse INSTANTANÉE avec le modèle en mémoire"""
        if not self.is_ready:
            return "❌ Agent non prêt - Modèle non chargé"
        
        try:
            print(f"⚡ Génération INSTANTANÉE avec modèle en mémoire...")
            
            start_time = time.time()
            
            # Utilisation du modèle déjà en mémoire
            inputs = self.tokenizer(prompt, return_tensors="pt", truncation=True, max_length=2048)
            
            import torch
            with torch.no_grad():
                outputs = self.model.generate(
                    **inputs,
                    max_new_tokens=max_tokens,
                    temperature=temperature,
                    do_sample=True,
                    top_p=0.9,
                    pad_token_id=self.tokenizer.eos_token_id
                )
            
            # Décoder la réponse
            response_tokens = outputs[0][inputs['input_ids'].shape[1]:]
            response_text = self.tokenizer.decode(response_tokens, skip_special_tokens=True)
            
            end_time = time.time()
            processing_time = end_time - start_time
            
            # Statistiques
            num_tokens = len(response_tokens)
            speed = num_tokens / processing_time if processing_time > 0 else 0
            
            print(f"✅ Réponse générée en {processing_time*1000:.0f}ms")
            print(f"⚡ Vitesse: {speed:.1f} tokens/sec")
            print(f"📊 Tokens: {num_tokens}")
            
            return response_text.strip()
            
        except Exception as e:
            print(f"❌ Erreur génération: {e}")
            return f"❌ Erreur: {e}"
    
    def run_server_mode(self):
        """Mode serveur - Écoute stdin en continu"""
        print("🚀 MODE SERVEUR ACTIF")
        print("📡 Écoute des requêtes via stdin...")
        print("")
        
        while self.running:
            try:
                # Lire une ligne depuis stdin
                line = sys.stdin.readline()
                if not line:
                    break
                
                prompt = line.strip()
                if not prompt:
                    continue
                
                if prompt.lower() in ['quit', 'exit', 'stop']:
                    break
                
                # Générer réponse
                response = self.generate_response(prompt)
                
                # Envoyer réponse via stdout
                print(f"RESPONSE:{response}")
                sys.stdout.flush()
                
            except KeyboardInterrupt:
                break
            except Exception as e:
                print(f"❌ Erreur serveur: {e}")
        
        print("🛑 Serveur arrêté")

def main():
    """Fonction principale"""
    
    # Mode API pour intégration serveur
    if len(sys.argv) > 1 and sys.argv[1] == "--server":
        agent = AgentR18BPersistent()
        if agent.is_ready:
            agent.run_server_mode()
        else:
            sys.exit(1)
    
    # Mode test simple
    elif len(sys.argv) > 1 and sys.argv[1] == "--test":
        agent = AgentR18BPersistent()
        if agent.is_ready:
            response = agent.generate_response("Bonjour, qui êtes-vous ?")
            print(f"Test réponse: {response}")
            sys.exit(0)
        else:
            sys.exit(1)
    
    # Mode interactif
    else:
        print("🚀 AGENT R1 8B PERSISTANT")
        print("========================")
        print("Options:")
        print("  --server  : Mode serveur (écoute stdin)")
        print("  --test    : Test rapide")
        print("  (aucun)   : Mode interactif")
        print("")
        
        agent = AgentR18BPersistent()
        if not agent.is_ready:
            return
        
        print("💬 Mode conversation (tapez 'quit' pour quitter)")
        while True:
            try:
                user_input = input("👤 Vous: ").strip()
                if user_input.lower() in ['quit', 'exit']:
                    break
                if user_input:
                    response = agent.generate_response(user_input)
                    print(f"🧠 JARVIS: {response}")
                    print("")
            except KeyboardInterrupt:
                break

if __name__ == "__main__":
    main()
