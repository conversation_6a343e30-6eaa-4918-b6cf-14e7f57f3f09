#!/usr/bin/env python3
"""
Script pour charger et utiliser le vrai modèle DeepSeek R1 8B
"""
import sys
import os
from transformers import AutoTokenizer, AutoModelForCausalLM
import torch

MODEL_PATH = "/Volumes/seagate/Louna_Electron_Latest/models/DeepSeek-R1-Distill-Llama-8B"

def load_model():
    print("🔄 Chargement du tokenizer...")
    tokenizer = AutoTokenizer.from_pretrained(MODEL_PATH)
    
    print("🔄 Chargement du modèle...")
    model = AutoModelForCausalLM.from_pretrained(
        MODEL_PATH,
        torch_dtype=torch.bfloat16,
        device_map="auto"
    )
    
    return tokenizer, model

def generate_response(tokenizer, model, prompt):
    inputs = tokenizer(prompt, return_tensors="pt")
    
    with torch.no_grad():
        outputs = model.generate(
            inputs.input_ids,
            max_new_tokens=200,
            temperature=0.7,
            do_sample=True,
            pad_token_id=tokenizer.eos_token_id
        )
    
    response = tokenizer.decode(outputs[0], skip_special_tokens=True)
    return response.replace(prompt, "").strip()

if __name__ == "__main__":
    if "--test" in sys.argv:
        try:
            tokenizer, model = load_model()
            print("✅ Modèle chargé avec succès")
            sys.exit(0)
        except Exception as e:
            print(f"❌ Erreur: {e}")
            sys.exit(1)
    
    elif "--generate" in sys.argv:
        try:
            prompt = input()
            tokenizer, model = load_model()
            response = generate_response(tokenizer, model, prompt)
            print(response)
        except Exception as e:
            print(f"❌ Erreur génération: {e}")
            sys.exit(1)
