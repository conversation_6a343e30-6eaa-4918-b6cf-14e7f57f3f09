/**
 * 🤖 JARVIS REAL DEEPSEEK R1 8B CONNECTOR
 * Connexion AUTHENTIQUE au vrai modèle DeepSeek R1 8B
 * VRAI AGENT IA - PAS DE SIMULATION !
 */

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');
const JarvisAutoAdaptationTurbo = require('./jarvis-auto-adaptation-turbo');

class JarvisRealDeepSeekConnector {
    constructor() {
        this.modelPath = '/Volumes/seagate/Louna_Electron_Latest/models/DeepSeek-R1-Distill-Llama-8B';
        this.isLoaded = false;
        this.isReal = true; // VRAI MODÈLE !

        console.log('🤖 === CONNEXION AU VRAI DEEPSEEK R1 8B ===');
        console.log('📁 Modèle:', this.modelPath);
        console.log('🧠 AUTO-ADAPTATION TURBO + CODE VIVANT');

        // Initialisation AUTO-ADAPTATION TURBO
        this.autoAdaptation = new JarvisAutoAdaptationTurbo();

        this.validateModel();
    }
    
    validateModel() {
        console.log('🔍 Validation du vrai modèle...');
        
        const configPath = path.join(this.modelPath, 'config.json');
        const tokenizerPath = path.join(this.modelPath, 'tokenizer.json');
        
        if (!fs.existsSync(configPath)) {
            throw new Error('❌ Config du modèle non trouvée');
        }
        
        if (!fs.existsSync(tokenizerPath)) {
            throw new Error('❌ Tokenizer du modèle non trouvé');
        }
        
        const config = JSON.parse(fs.readFileSync(configPath, 'utf8'));
        
        console.log('✅ Modèle validé:');
        console.log(`   🧠 Architecture: ${config.architectures[0]}`);
        console.log(`   📊 Taille cachée: ${config.hidden_size}`);
        console.log(`   🔢 Couches: ${config.num_hidden_layers}`);
        console.log(`   📝 Vocabulaire: ${config.vocab_size} tokens`);
        console.log(`   📏 Contexte max: ${config.max_position_embeddings} tokens`);
        
        return true;
    }
    
    async loadModel() {
        if (this.isLoaded) {
            console.log('✅ Modèle déjà chargé');
            return true;
        }

        console.log('🚀 Chargement du VRAI modèle DeepSeek R1 8B...');
        console.log('🔥 CONNEXION DIRECTE - SANS OLLAMA !');

        // Méthode DIRECTE: Python/Transformers UNIQUEMENT
        if (await this.tryDirectPythonMethod()) {
            return true;
        }

        console.log('⚠️ Connexion directe non disponible');
        console.log('💡 Solutions:');
        console.log('   1. Installer Python + transformers: pip install transformers torch');
        console.log('   2. Vérifier le modèle DeepSeek R1 8B local');

        return false;
    }
    
    async tryDirectPythonMethod() {
        console.log('🐍 CONNEXION DIRECTE via Python/Transformers...');
        console.log('🔥 SANS OLLAMA - BRANCHEMENT DIRECT !');

        try {
            // Vérifier si Python est disponible
            const pythonCheck = await this.runCommand('python3', ['--version']);
            if (!pythonCheck.success) {
                console.log('❌ Python3 non disponible');
                return false;
            }

            // Démarrer le serveur Python direct
            const serverStarted = await this.startDirectPythonServer();
            if (serverStarted) {
                console.log('✅ Serveur Python direct démarré');
                this.loadMethod = 'direct_python';
                this.isLoaded = true;
                return true;
            }

        } catch (error) {
            console.log('❌ Connexion directe Python échouée:', error.message);
        }

        return false;
    }
    
    async startDirectPythonServer() {
        console.log('🚀 Connexion DIRECTE Python R1 8B...');
        console.log('🔥 MÉTHODE SIMPLIFIÉE - SANS SERVEUR HTTP !');

        try {
            const { spawn } = require('child_process');

            // Essayer d'abord la version simple pour test rapide
            const simpleScript = path.join(__dirname, 'jarvis-r1-8b-simple.py');
            const fullScript = path.join(__dirname, 'jarvis-r1-8b-authentique.py');

            let pythonScript = null;

            // Test version simple d'abord
            if (fs.existsSync(simpleScript)) {
                console.log('🧪 Test version simple R1 8B...');
                const testResult = await this.runCommand('python3', [simpleScript, '--test']);

                if (testResult.success) {
                    console.log('✅ Version simple R1 8B validée');
                    pythonScript = simpleScript;
                }
            }

            // Si pas de version simple, essayer la version complète
            if (!pythonScript && fs.existsSync(fullScript)) {
                console.log('🧠 Test version complète R1 8B...');
                const testResult = await this.runCommand('python3', [fullScript, '--test']);

                if (testResult.success) {
                    console.log('✅ Version complète R1 8B validée');
                    pythonScript = fullScript;
                }
            }

            if (pythonScript) {
                this.pythonScriptPath = pythonScript;
                this.loadMethod = 'direct_python';
                console.log('🔥 CONNEXION DIRECTE ÉTABLIE - SANS OLLAMA !');
                return true;
            } else {
                console.log('❌ Aucun script Python R1 8B fonctionnel');
                return false;
            }

        } catch (error) {
            console.log('❌ Erreur validation Python:', error.message);
            return false;
        }
    }
    
    async tryNodeMethod() {
        console.log('📦 Tentative chargement via Node.js...');
        
        try {
            // Essayer d'utiliser @xenova/transformers
            const { pipeline } = require('@xenova/transformers');
            
            console.log('🔄 Chargement du pipeline...');
            this.pipeline = await pipeline('text-generation', this.modelPath);
            
            console.log('✅ Modèle chargé via Transformers.js');
            this.loadMethod = 'transformers-js';
            this.isLoaded = true;
            return true;
            
        } catch (error) {
            console.log('❌ Transformers.js non disponible');
            console.log('💡 Installer avec: npm install @xenova/transformers');
        }
        
        return false;
    }
    
    async generateResponse(prompt) {
        if (!this.isLoaded) {
            console.log('🔄 Chargement du modèle...');
            const loaded = await this.loadModel();
            if (!loaded) {
                return this.generateFallbackResponse(prompt);
            }
        }
        
        console.log('🤖 Génération avec VRAI DeepSeek R1 8B...');
        
        try {
            switch (this.loadMethod) {
                case 'direct_python':
                    return await this.generateWithDirectPython(prompt);

                default:
                    return this.generateFallbackResponse(prompt);
            }
        } catch (error) {
            console.error('❌ Erreur génération:', error);
            return this.generateFallbackResponse(prompt);
        }
    }
    
    async generateWithDirectPython(prompt) {
        console.log('🐍 GÉNÉRATION DIRECTE via Python R1 8B...');
        console.log('🔥 CONNEXION DIRECTE - SANS OLLAMA !');
        console.log('🧠 AUTO-ADAPTATION TURBO ACTIVE');

        try {
            // Vérification état auto-adaptation
            const adaptationStatus = this.autoAdaptation.getSystemStatus();
            const thermalState = adaptationStatus.thermal_state;

            // Adaptation du prompt selon état thermique
            let adaptedPrompt = prompt;
            if (thermalState.saturation_level > 0.8) {
                // Mode économie : prompt plus court
                adaptedPrompt = prompt.substring(0, Math.min(prompt.length, 500));
                console.log('⚡ Mode économie activé - Prompt adapté');
            } else if (thermalState.saturation_level < 0.3) {
                // Mode performance : prompt enrichi
                adaptedPrompt = `[TURBO MODE] ${prompt}`;
                console.log('⚡ Mode TURBO activé - Performance maximale');
            }

            // Appel direct au script Python avec le prompt adapté
            const result = await this.runCommand('python3', [this.pythonScriptPath, '--generate'], adaptedPrompt);

            if (result.success && result.output) {
                // Enrichissement de la réponse avec métriques auto-adaptation
                const response = this.enrichResponseWithAdaptation(result.output, adaptationStatus);
                console.log('✅ Réponse générée avec AUTO-ADAPTATION');
                return response;
            } else {
                console.log('❌ Échec génération:', result.error);
                throw new Error(`Échec génération: ${result.error}`);
            }

        } catch (error) {
            console.error('❌ Erreur génération Python:', error);
            throw new Error(`Erreur génération directe: ${error.message}`);
        }
    }

    enrichResponseWithAdaptation(originalResponse, adaptationStatus) {
        const thermalState = adaptationStatus.thermal_state;
        const turboLevel = adaptationStatus.turbo_level;
        const cycles = adaptationStatus.living_code_cycles;

        return `🤖 **RÉPONSE DU VRAI DEEPSEEK R1 8B (CONNEXION DIRECTE + AUTO-ADAPTATION)**

🧠 **AUTO-ADAPTATION TURBO ACTIVE:**
- 🌡️ État thermique: ${thermalState.temperature.toFixed(1)}°C
- ⚡ Niveau TURBO: ${turboLevel}/10
- 🧬 Cycles évolution: ${cycles}
- 💾 Pression mémoire: ${(thermalState.pressure * 100).toFixed(1)}%
- 🔄 Taux adaptation: ${(thermalState.adaptation_rate * 100).toFixed(1)}%

🔥 **CONNEXION DIRECTE - SANS OLLAMA !**

${originalResponse}

✅ **CODE VIVANT + MÉMOIRE THERMIQUE TURBO INTÉGRÉS !**`;
    }
    
    async generateWithTransformersJS(prompt) {
        console.log('📦 Génération via Transformers.js...');
        
        const result = await this.pipeline(prompt, {
            max_new_tokens: 200,
            temperature: 0.7,
            do_sample: true
        });
        
        if (result && result[0] && result[0].generated_text) {
            const response = result[0].generated_text.replace(prompt, '').trim();
            return `🤖 **RÉPONSE DU VRAI DEEPSEEK R1 8B (via Transformers.js)**\n\n${response}`;
        }
        
        throw new Error('Échec génération Transformers.js');
    }
    
    generateFallbackResponse(prompt) {
        return `🤖 **DEEPSEEK R1 8B (Mode Fallback)**\n\n` +
               `Le vrai modèle DeepSeek R1 8B est présent sur votre système mais nécessite ` +
               `une connexion directe Python+Transformers pour fonctionner.\n\n` +
               `📁 Modèle disponible: ${this.modelPath}\n` +
               `💡 Votre question: "${prompt}"\n\n` +
               `🔥 CONNEXION DIRECTE - SANS OLLAMA !\n` +
               `Pour activer le vrai modèle:\n` +
               `1. pip install transformers torch\n` +
               `2. Vérifier que le modèle DeepSeek R1 8B est téléchargé\n` +
               `3. Démarrer le serveur Python direct`;
    }
    
    async runCommand(command, args, input = null) {
        return new Promise((resolve) => {
            const process = spawn(command, args, { stdio: ['pipe', 'pipe', 'pipe'] });
            
            let output = '';
            let error = '';
            
            process.stdout.on('data', (data) => {
                output += data.toString();
            });
            
            process.stderr.on('data', (data) => {
                error += data.toString();
            });
            
            if (input) {
                process.stdin.write(input);
                process.stdin.end();
            }
            
            process.on('close', (code) => {
                resolve({
                    success: code === 0,
                    output: output.trim(),
                    error: error.trim(),
                    code
                });
            });
            
            // Timeout après 30 secondes
            setTimeout(() => {
                process.kill();
                resolve({ success: false, error: 'Timeout', code: -1 });
            }, 30000);
        });
    }
    
    generatePythonScript() {
        return `#!/usr/bin/env python3
"""
Script pour charger et utiliser le vrai modèle DeepSeek R1 8B
"""
import sys
import os
from transformers import AutoTokenizer, AutoModelForCausalLM
import torch

MODEL_PATH = "${this.modelPath}"

def load_model():
    print("🔄 Chargement du tokenizer...")
    tokenizer = AutoTokenizer.from_pretrained(MODEL_PATH)
    
    print("🔄 Chargement du modèle...")
    model = AutoModelForCausalLM.from_pretrained(
        MODEL_PATH,
        torch_dtype=torch.bfloat16,
        device_map="auto"
    )
    
    return tokenizer, model

def generate_response(tokenizer, model, prompt):
    inputs = tokenizer(prompt, return_tensors="pt")
    
    with torch.no_grad():
        outputs = model.generate(
            inputs.input_ids,
            max_new_tokens=200,
            temperature=0.7,
            do_sample=True,
            pad_token_id=tokenizer.eos_token_id
        )
    
    response = tokenizer.decode(outputs[0], skip_special_tokens=True)
    return response.replace(prompt, "").strip()

if __name__ == "__main__":
    if "--test" in sys.argv:
        try:
            tokenizer, model = load_model()
            print("✅ Modèle chargé avec succès")
            sys.exit(0)
        except Exception as e:
            print(f"❌ Erreur: {e}")
            sys.exit(1)
    
    elif "--generate" in sys.argv:
        try:
            prompt = input()
            tokenizer, model = load_model()
            response = generate_response(tokenizer, model, prompt)
            print(response)
        except Exception as e:
            print(f"❌ Erreur génération: {e}")
            sys.exit(1)
`;
    }
    
    getStatus() {
        const baseStatus = {
            isReal: true,
            modelPath: this.modelPath,
            isLoaded: this.isLoaded,
            loadMethod: this.loadMethod || 'none',
            modelExists: fs.existsSync(this.modelPath)
        };

        // Ajout statut auto-adaptation si disponible
        if (this.autoAdaptation) {
            baseStatus.auto_adaptation = this.autoAdaptation.getSystemStatus();
        }

        return baseStatus;
    }

    // Méthode pour obtenir les métriques détaillées
    getDetailedMetrics() {
        if (!this.autoAdaptation) {
            return { error: 'Auto-adaptation non initialisée' };
        }

        const status = this.autoAdaptation.getSystemStatus();

        return {
            system_health: {
                thermal_temperature: status.thermal_state.temperature,
                memory_pressure: status.thermal_state.pressure,
                saturation_level: status.thermal_state.saturation_level,
                adaptation_rate: status.thermal_state.adaptation_rate
            },
            turbo_performance: {
                turbo_level: status.turbo_level,
                max_turbo_level: 10,
                turbo_effectiveness: status.performance_metrics.turbo_effectiveness
            },
            living_code: {
                evolution_cycles: status.living_code_cycles,
                adaptation_patterns: status.adaptation_patterns,
                survival_strategies: status.survival_strategies
            },
            machine_optimization: {
                cpu_cores: status.machine_profile.cpu_cores,
                total_memory: status.machine_profile.total_memory,
                neural_engine: status.machine_profile.neural_engine,
                memory_ratio: status.machine_profile.memory_ratio
            },
            emergency_systems: {
                emergency_protocols: status.emergency_protocols,
                anti_saturation_active: status.active
            }
        };
    }
}

module.exports = JarvisRealDeepSeekConnector;
