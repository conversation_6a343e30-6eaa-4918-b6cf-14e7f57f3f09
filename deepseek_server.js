#!/usr/bin/env node
/**
 * 🤖 SERVEUR DEEPSEEK R1 8B POUR INTERFACE CLAUDE
 * Serveur Node.js qui connecte le modèle DeepSeek R1 8B à l'interface Claude
 */

const express = require('express');
const cors = require('cors');
const path = require('path');

// Import du connecteur DeepSeek R1 8B
const JarvisRealDeepSeekConnector = require('./core/jarvis-real-deepseek-connector.js');

const app = express();
const PORT = 8080;

// Middleware
app.use(cors());
app.use(express.json());

// Instance du connecteur DeepSeek
let deepseekConnector = null;

// Initialisation du connecteur
async function initializeDeepSeek() {
    try {
        console.log('🚀 === SERVEUR DEEPSEEK R1 8B POUR CLAUDE ===');
        console.log('🔌 Initialisation du connecteur...');
        
        deepseekConnector = new JarvisRealDeepSeekConnector();
        
        console.log('🔄 Chargement du modèle...');
        const loaded = await deepseekConnector.loadModel();
        
        if (loaded) {
            console.log('✅ Modèle DeepSeek R1 8B chargé avec succès !');
            console.log(`📊 Statut:`, deepseekConnector.getStatus());
        } else {
            console.log('⚠️ Modèle non chargé - Mode fallback activé');
        }
        
    } catch (error) {
        console.error('❌ Erreur initialisation:', error);
        console.log('💡 Le serveur fonctionnera en mode fallback');
    }
}

// Route principale pour les completions
app.post('/v1/chat/completions', async (req, res) => {
    try {
        const { messages, temperature = 0.7, max_tokens = 500 } = req.body;
        
        if (!messages || !Array.isArray(messages) || messages.length === 0) {
            return res.status(400).json({
                error: 'Messages requis'
            });
        }
        
        // Extraire le dernier message utilisateur
        const userMessage = messages[messages.length - 1];
        const prompt = userMessage.content;
        
        console.log(`🤖 Requête DeepSeek R1 8B: "${prompt.substring(0, 50)}..."`);
        
        let response;
        
        if (deepseekConnector) {
            // Utiliser le vrai connecteur
            response = await deepseekConnector.generateResponse(prompt);
        } else {
            // Mode fallback
            response = `🤖 **DEEPSEEK R1 8B (Mode Serveur)**\n\n` +
                      `Connecteur DeepSeek R1 8B en cours d'initialisation.\n\n` +
                      `📝 Votre question: "${prompt}"\n\n` +
                      `🔧 Pour activer le vrai modèle:\n` +
                      `1. Installer Ollama: brew install ollama\n` +
                      `2. Charger le modèle: ollama pull deepseek-r1:8b\n` +
                      `3. Redémarrer ce serveur`;
        }
        
        // Format de réponse compatible OpenAI
        res.json({
            id: `chatcmpl-${Date.now()}`,
            object: 'chat.completion',
            created: Math.floor(Date.now() / 1000),
            model: 'deepseek-r1-8b',
            choices: [{
                index: 0,
                message: {
                    role: 'assistant',
                    content: response
                },
                finish_reason: 'stop'
            }],
            usage: {
                prompt_tokens: prompt.length / 4, // Estimation
                completion_tokens: response.length / 4, // Estimation
                total_tokens: (prompt.length + response.length) / 4
            }
        });
        
    } catch (error) {
        console.error('❌ Erreur génération:', error);
        
        res.status(500).json({
            error: {
                message: `Erreur du modèle DeepSeek R1 8B: ${error.message}`,
                type: 'model_error',
                code: 'deepseek_error'
            }
        });
    }
});

// Route de statut
app.get('/status', (req, res) => {
    const status = deepseekConnector ? deepseekConnector.getStatus() : {
        isReal: false,
        modelPath: 'Non initialisé',
        isLoaded: false,
        loadMethod: 'none',
        modelExists: false
    };
    
    res.json({
        server: 'DeepSeek R1 8B Server',
        version: '1.0.0',
        status: 'running',
        model: status,
        endpoints: {
            chat: '/v1/chat/completions',
            status: '/status',
            adaptation: '/adaptation-metrics'
        }
    });
});

// Route métriques auto-adaptation
app.get('/adaptation-metrics', (req, res) => {
    const metrics = deepseekConnector ? deepseekConnector.getDetailedMetrics() : {
        error: 'Connecteur non disponible',
        message: 'Le système d\'auto-adaptation n\'est pas encore initialisé'
    };

    res.json(metrics);
});

// Route de santé
app.get('/health', (req, res) => {
    res.json({ status: 'healthy', timestamp: new Date().toISOString() });
});

// Démarrage du serveur
async function startServer() {
    // Initialiser DeepSeek
    await initializeDeepSeek();
    
    // Démarrer le serveur
    app.listen(PORT, () => {
        console.log('');
        console.log('🎉 === SERVEUR DEEPSEEK R1 8B DÉMARRÉ ===');
        console.log(`🌐 URL: http://localhost:${PORT}`);
        console.log(`🔗 API: http://localhost:${PORT}/v1/chat/completions`);
        console.log(`📊 Statut: http://localhost:${PORT}/status`);
        console.log('');
        console.log('🎨 Interface Claude peut maintenant se connecter !');
        console.log('🔌 Modèle DeepSeek R1 8B prêt pour les requêtes');
        console.log('');
    });
}

// Gestion des erreurs
process.on('uncaughtException', (error) => {
    console.error('❌ Erreur non gérée:', error);
});

process.on('unhandledRejection', (reason, promise) => {
    console.error('❌ Promesse rejetée:', reason);
});

// Démarrage
startServer().catch(error => {
    console.error('❌ Erreur démarrage serveur:', error);
    process.exit(1);
});
