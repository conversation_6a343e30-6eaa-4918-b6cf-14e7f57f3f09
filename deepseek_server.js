#!/usr/bin/env node
/**
 * 🤖 SERVEUR DEEPSEEK R1 8B POUR INTERFACE CLAUDE
 * Serveur Node.js qui connecte le modèle DeepSeek R1 8B à l'interface Claude
 */

const express = require('express');
const cors = require('cors');
const path = require('path');

// Import du connecteur DeepSeek R1 8B
const JarvisRealDeepSeekConnector = require('./core/jarvis-real-deepseek-connector.js');

const app = express();
const PORT = 8080;

// Middleware
app.use(cors());
app.use(express.json());

// Instance du connecteur DeepSeek
let deepseekConnector = null;

// Initialisation du connecteur
async function initializeDeepSeek() {
    try {
        console.log('🚀 === SERVEUR DEEPSEEK R1 8B POUR CLAUDE ===');
        console.log('🔌 Initialisation du connecteur...');
        
        deepseekConnector = new JarvisRealDeepSeekConnector();
        
        console.log('🔄 Chargement du modèle...');
        const loaded = await deepseekConnector.loadModel();
        
        if (loaded) {
            console.log('✅ Modèle DeepSeek R1 8B chargé avec succès !');
            console.log(`📊 Statut:`, deepseekConnector.getStatus());
        } else {
            console.log('⚠️ Modèle non chargé - Mode fallback activé');
        }
        
    } catch (error) {
        console.error('❌ Erreur initialisation:', error);
        console.log('💡 Le serveur fonctionnera en mode fallback');
    }
}

// Route principale pour les completions
app.post('/v1/chat/completions', async (req, res) => {
    try {
        const { messages, temperature = 0.7, max_tokens = 500 } = req.body;
        
        if (!messages || !Array.isArray(messages) || messages.length === 0) {
            return res.status(400).json({
                error: 'Messages requis'
            });
        }
        
        // Extraire le dernier message utilisateur
        const userMessage = messages[messages.length - 1];
        const prompt = userMessage.content;
        
        console.log(`🤖 Requête DeepSeek R1 8B: "${prompt.substring(0, 50)}..."`);
        
        let response;
        
        if (deepseekConnector) {
            // Utiliser le vrai connecteur
            response = await deepseekConnector.generateResponse(prompt);
        } else {
            // Mode fallback
            response = `🤖 **DEEPSEEK R1 8B (Mode Serveur)**\n\n` +
                      `Connecteur DeepSeek R1 8B en cours d'initialisation.\n\n` +
                      `📝 Votre question: "${prompt}"\n\n` +
                      `🔧 Pour activer le vrai modèle:\n` +
                      `1. Installer Ollama: brew install ollama\n` +
                      `2. Charger le modèle: ollama pull deepseek-r1:8b\n` +
                      `3. Redémarrer ce serveur`;
        }
        
        // Format de réponse compatible OpenAI
        res.json({
            id: `chatcmpl-${Date.now()}`,
            object: 'chat.completion',
            created: Math.floor(Date.now() / 1000),
            model: 'deepseek-r1-8b',
            choices: [{
                index: 0,
                message: {
                    role: 'assistant',
                    content: response
                },
                finish_reason: 'stop'
            }],
            usage: {
                prompt_tokens: prompt.length / 4, // Estimation
                completion_tokens: response.length / 4, // Estimation
                total_tokens: (prompt.length + response.length) / 4
            }
        });
        
    } catch (error) {
        console.error('❌ Erreur génération:', error);
        
        res.status(500).json({
            error: {
                message: `Erreur du modèle DeepSeek R1 8B: ${error.message}`,
                type: 'model_error',
                code: 'deepseek_error'
            }
        });
    }
});

// Route de statut
app.get('/status', (req, res) => {
    const status = deepseekConnector ? deepseekConnector.getStatus() : {
        isReal: false,
        modelPath: 'Non initialisé',
        isLoaded: false,
        loadMethod: 'none',
        modelExists: false
    };

    const serverInfo = {
        server: 'DeepSeek R1 8B Server',
        version: '1.0.0',
        status: 'running',
        model: status,
        endpoints: {
            chat: '/v1/chat/completions',
            status: '/status',
            adaptation: '/adaptation-metrics',
            fiche_technique: '/fiche-technique'
        }
    };

    // Si c'est une requête JSON (API)
    if (req.headers.accept && req.headers.accept.includes('application/json')) {
        res.json(serverInfo);
        return;
    }

    // Interface HTML pour le navigateur
    res.setHeader('Content-Type', 'text/html; charset=utf-8');
    res.send(`
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>JARVIS - Status Système</title>
            <style>
                body {
                    font-family: 'Courier New', monospace;
                    background: #0a0a0a;
                    color: #00ff00;
                    padding: 20px;
                    line-height: 1.6;
                }
                h1, h2 { color: #00ffff; text-shadow: 0 0 10px #00ffff; }
                .nav-buttons {
                    text-align: center;
                    margin: 20px 0;
                    padding: 20px;
                    border: 1px solid #00ff00;
                    border-radius: 10px;
                    background: rgba(0, 20, 0, 0.3);
                }
                .btn {
                    background: linear-gradient(45deg, #003300, #006600);
                    border: 1px solid #00ff00;
                    color: #00ff00;
                    padding: 10px 20px;
                    margin: 5px;
                    text-decoration: none;
                    border-radius: 5px;
                    display: inline-block;
                    transition: all 0.3s ease;
                }
                .btn:hover { box-shadow: 0 0 15px #00ff00; }
                .btn-metrics { border-color: #00ffff; color: #00ffff; background: linear-gradient(45deg, #003333, #006666); }
                .btn-fiche { border-color: #ff00ff; color: #ff00ff; background: linear-gradient(45deg, #330033, #660066); }
                .btn-interface { border-color: #ffff00; color: #ffff00; background: linear-gradient(45deg, #333300, #666600); }
                pre {
                    background: #001100;
                    padding: 15px;
                    border-radius: 5px;
                    border: 1px solid #00ff00;
                    overflow-x: auto;
                }
                .status-ok { color: #00ff00; }
                .status-warning { color: #ffff00; }
                .status-panel {
                    background: rgba(0, 20, 0, 0.3);
                    border: 1px solid #00ff00;
                    border-radius: 10px;
                    padding: 15px;
                    margin: 15px 0;
                }
            </style>
        </head>
        <body>
            <div style="text-align: center;">
                <h1>📊 JARVIS - STATUS SYSTÈME</h1>
                <p style="color: #00ffff;">Surveillance en Temps Réel - Auto-Adaptation TURBO</p>
                <p style="color: #ffff00;">Mise à jour: ${new Date().toLocaleString('fr-FR')}</p>
            </div>

            <div class="nav-buttons">
                <h3 style="color: #00ffff; margin-bottom: 15px;">🔗 NAVIGATION JARVIS</h3>
                <a href="/fiche-technique" class="btn btn-fiche">📋 Fiche Technique</a>
                <a href="/adaptation-metrics" class="btn btn-metrics">⚡ Métriques Live</a>
                <a href="file:///Volumes/seagate/Louna_Electron_Latest/interface-jarvis-complete.html" class="btn btn-interface">🏠 Interface JARVIS</a>
                <button onclick="location.reload()" class="btn">🔄 Actualiser</button>
            </div>

            <div class="status-panel">
                <h2>🖥️ Statut Serveur</h2>
                <p>Serveur: <span class="status-ok">${serverInfo.server}</span></p>
                <p>Version: <span class="status-ok">${serverInfo.version}</span></p>
                <p>État: <span class="status-ok">${serverInfo.status}</span></p>
                <p>Uptime: <span class="status-ok">${Math.floor(process.uptime())} secondes</span></p>
                <p>Timestamp: <span class="status-ok">${new Date().toLocaleString('fr-FR')}</span></p>
            </div>

            <div class="status-panel">
                <h2>🤖 Statut Modèle DeepSeek R1 8B</h2>
                <p>Modèle Réel: <span class="${status.isReal ? 'status-ok' : 'status-warning'}">${status.isReal ? '✅ OUI' : '❌ NON'}</span></p>
                <p>Chargé: <span class="${status.isLoaded ? 'status-ok' : 'status-warning'}">${status.isLoaded ? '✅ OUI' : '❌ NON'}</span></p>
                <p>Méthode: <span class="status-ok">${status.loadMethod}</span></p>
                <p>Modèle Existe: <span class="${status.modelExists ? 'status-ok' : 'status-warning'}">${status.modelExists ? '✅ OUI' : '❌ NON'}</span></p>
                <p>Chemin: <span style="color: #ffff00; font-size: 12px;">${status.modelPath}</span></p>
            </div>

            ${status.auto_adaptation ? `
                <div class="status-panel">
                    <h2>🧠 Auto-Adaptation TURBO</h2>
                    <p>Système Actif: <span class="status-ok">✅ OUI</span></p>
                    <p>Niveau TURBO: <span class="status-ok">${status.auto_adaptation.turbo_level}/10</span></p>
                    <p>Cycles Évolution: <span class="status-ok">${status.auto_adaptation.living_code_cycles}</span></p>
                    <p>Neural Engine: <span class="status-ok">${status.auto_adaptation.machine_profile.neural_engine}</span></p>
                    <p>CPU: <span class="status-ok">${status.auto_adaptation.machine_profile.cpu_cores} cores</span></p>
                    <p>RAM: <span class="status-ok">${status.auto_adaptation.machine_profile.total_memory}GB</span></p>
                </div>
            ` : ''}

            <h2>🔗 Endpoints API Disponibles</h2>
            <pre>${JSON.stringify(serverInfo.endpoints, null, 2)}</pre>

            <h2>📊 Données Complètes</h2>
            <pre>${JSON.stringify(serverInfo, null, 2)}</pre>

            <div style="text-align: center; margin-top: 30px; padding: 20px; border: 1px solid #00ff00; border-radius: 10px; background: rgba(0, 20, 0, 0.3);">
                <p style="color: #00ffff;">🔥 SYSTÈME AUTHENTIQUE - CONNEXION DIRECTE SANS OLLAMA 🔥</p>
                <p style="color: #00ff00;">✅ AUTO-ADAPTATION TURBO ACTIVE - CODE VIVANT EN ÉVOLUTION ✅</p>
            </div>

            <script>
                // Auto-actualisation toutes les 10 secondes
                setTimeout(() => location.reload(), 10000);
            </script>
        </body>
        </html>
    `);
});

// Route métriques auto-adaptation
app.get('/adaptation-metrics', (req, res) => {
    const metrics = deepseekConnector ? deepseekConnector.getDetailedMetrics() : {
        error: 'Connecteur non disponible',
        message: 'Le système d\'auto-adaptation n\'est pas encore initialisé'
    };

    // Si c'est une requête JSON (API)
    if (req.headers.accept && req.headers.accept.includes('application/json')) {
        res.json(metrics);
        return;
    }

    // Interface HTML pour le navigateur
    res.setHeader('Content-Type', 'text/html; charset=utf-8');
    res.send(`
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>JARVIS - Métriques Auto-Adaptation TURBO</title>
            <style>
                body {
                    font-family: 'Courier New', monospace;
                    background: #0a0a0a;
                    color: #00ff00;
                    padding: 20px;
                    line-height: 1.6;
                }
                h1, h2 { color: #00ffff; text-shadow: 0 0 10px #00ffff; }
                .nav-buttons {
                    text-align: center;
                    margin: 20px 0;
                    padding: 20px;
                    border: 1px solid #00ff00;
                    border-radius: 10px;
                    background: rgba(0, 20, 0, 0.3);
                }
                .btn {
                    background: linear-gradient(45deg, #003300, #006600);
                    border: 1px solid #00ff00;
                    color: #00ff00;
                    padding: 10px 20px;
                    margin: 5px;
                    text-decoration: none;
                    border-radius: 5px;
                    display: inline-block;
                    transition: all 0.3s ease;
                }
                .btn:hover { box-shadow: 0 0 15px #00ff00; }
                .btn-status { border-color: #00ffff; color: #00ffff; background: linear-gradient(45deg, #003333, #006666); }
                .btn-fiche { border-color: #ff00ff; color: #ff00ff; background: linear-gradient(45deg, #330033, #660066); }
                .btn-interface { border-color: #ffff00; color: #ffff00; background: linear-gradient(45deg, #333300, #666600); }
                .metric-panel {
                    background: rgba(0, 20, 0, 0.3);
                    border: 1px solid #00ff00;
                    border-radius: 10px;
                    padding: 15px;
                    margin: 15px 0;
                }
                .metric-title { color: #00ffff; font-size: 18px; margin-bottom: 10px; }
                .metric-value { color: #ffff00; font-weight: bold; }
                .metric-good { color: #00ff00; }
                .metric-warning { color: #ffff00; }
                .metric-critical { color: #ff0000; }
                pre {
                    background: #001100;
                    padding: 15px;
                    border-radius: 5px;
                    border: 1px solid #00ff00;
                    overflow-x: auto;
                    font-size: 12px;
                }
            </style>
        </head>
        <body>
            <div style="text-align: center;">
                <h1>⚡ JARVIS - MÉTRIQUES AUTO-ADAPTATION TURBO</h1>
                <p style="color: #00ffff;">Surveillance Temps Réel - Code Vivant en Évolution</p>
                <p style="color: #ffff00;">Mise à jour: ${new Date().toLocaleString('fr-FR')}</p>
            </div>

            <div class="nav-buttons">
                <h3 style="color: #00ffff; margin-bottom: 15px;">🔗 NAVIGATION JARVIS</h3>
                <a href="/status" class="btn btn-status">📊 Status Système</a>
                <a href="/fiche-technique" class="btn btn-fiche">📋 Fiche Technique</a>
                <a href="file:///Volumes/seagate/Louna_Electron_Latest/interface-jarvis-complete.html" class="btn btn-interface">🏠 Interface JARVIS</a>
                <button onclick="location.reload()" class="btn">🔄 Actualiser</button>
            </div>

            ${metrics.error ? `
                <div class="metric-panel">
                    <div class="metric-title">❌ Erreur Système</div>
                    <p class="metric-critical">${metrics.error}</p>
                    <p>${metrics.message}</p>
                </div>
            ` : `
                <div class="metric-panel">
                    <div class="metric-title">🌡️ État Thermique Système</div>
                    <p>Température: <span class="metric-value">${metrics.system_health?.thermal_temperature?.toFixed(1) || '--'}°C</span></p>
                    <p>Pression Mémoire: <span class="metric-value">${((metrics.system_health?.memory_pressure || 0) * 100).toFixed(1)}%</span></p>
                    <p>Saturation: <span class="metric-value">${((metrics.system_health?.saturation_level || 0) * 100).toFixed(1)}%</span></p>
                    <p>Taux Adaptation: <span class="metric-value">${((metrics.system_health?.adaptation_rate || 0) * 100).toFixed(1)}%</span></p>
                </div>

                <div class="metric-panel">
                    <div class="metric-title">⚡ Performance TURBO KYBER</div>
                    <p>Niveau TURBO: <span class="metric-value">${metrics.turbo_performance?.turbo_level || 0}/${metrics.turbo_performance?.max_turbo_level || 10}</span></p>
                    <p>Efficacité: <span class="metric-value">${((metrics.turbo_performance?.turbo_effectiveness || 0) * 100).toFixed(1)}%</span></p>
                </div>

                <div class="metric-panel">
                    <div class="metric-title">🧬 Code Vivant Évolutif</div>
                    <p>Cycles Évolution: <span class="metric-value">${metrics.living_code?.evolution_cycles || 0}</span></p>
                    <p>Patterns Adaptation: <span class="metric-value">${metrics.living_code?.adaptation_patterns || 0}</span></p>
                    <p>Stratégies Survie: <span class="metric-value">${metrics.living_code?.survival_strategies || 0}</span></p>
                </div>

                <div class="metric-panel">
                    <div class="metric-title">🖥️ Optimisation Machine</div>
                    <p>CPU: <span class="metric-value">${metrics.machine_optimization?.cpu_cores || '--'} cores</span></p>
                    <p>RAM: <span class="metric-value">${metrics.machine_optimization?.total_memory || '--'}GB</span></p>
                    <p>Neural Engine: <span class="metric-value">${metrics.machine_optimization?.neural_engine || '--'}</span></p>
                </div>

                <div class="metric-panel">
                    <div class="metric-title">🛡️ Systèmes Protection</div>
                    <p>Anti-Saturation: <span class="metric-value">${metrics.emergency_systems?.anti_saturation_active ? '✅ ACTIF' : '❌ INACTIF'}</span></p>
                    <p>Protocoles Urgence: <span class="metric-value">${metrics.emergency_systems?.emergency_protocols || 0}</span></p>
                </div>
            `}

            <h2>📊 Données Complètes JSON</h2>
            <pre>${JSON.stringify(metrics, null, 2)}</pre>

            <div style="text-align: center; margin-top: 30px; padding: 20px; border: 1px solid #00ff00; border-radius: 10px; background: rgba(0, 20, 0, 0.3);">
                <p style="color: #00ffff;">🔥 AUTO-ADAPTATION TURBO EN TEMPS RÉEL 🔥</p>
                <p style="color: #00ff00;">✅ CODE VIVANT - ÉVOLUTION CONTINUE ACTIVE ✅</p>
            </div>

            <script>
                // Auto-actualisation toutes les 5 secondes
                setTimeout(() => location.reload(), 5000);
            </script>
        </body>
        </html>
    `);
});

// Route fiche technique complète
app.get('/fiche-technique', (req, res) => {
    const fs = require('fs');
    const path = require('path');

    try {
        const fichePath = path.join(__dirname, 'docs', 'JARVIS-AUTO-ADAPTATION-TURBO-FICHE-COMPLETE.md');

        if (fs.existsSync(fichePath)) {
            const ficheContent = fs.readFileSync(fichePath, 'utf8');

            // Retourner en HTML pour affichage dans l'interface
            res.setHeader('Content-Type', 'text/html; charset=utf-8');
            res.send(`
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset="UTF-8">
                    <title>JARVIS - Fiche Technique Auto-Adaptation TURBO</title>
                    <style>
                        body {
                            font-family: 'Courier New', monospace;
                            background: #0a0a0a;
                            color: #00ff00;
                            padding: 20px;
                            line-height: 1.6;
                        }
                        h1, h2, h3 { color: #00ffff; text-shadow: 0 0 10px #00ffff; }
                        h1 { font-size: 2em; text-align: center; }
                        table {
                            border-collapse: collapse;
                            width: 100%;
                            margin: 20px 0;
                            border: 1px solid #00ff00;
                        }
                        th, td {
                            border: 1px solid #00ff00;
                            padding: 8px;
                            text-align: left;
                        }
                        th { background: #001100; color: #00ffff; }
                        code {
                            background: #001100;
                            padding: 2px 4px;
                            border-radius: 3px;
                            color: #ffff00;
                        }
                        pre {
                            background: #001100;
                            padding: 15px;
                            border-radius: 5px;
                            overflow-x: auto;
                            border: 1px solid #00ff00;
                        }
                        .status-ok { color: #00ff00; }
                        .status-warning { color: #ffff00; }
                        .status-critical { color: #ff0000; }
                        .highlight {
                            background: #003300;
                            padding: 10px;
                            border-left: 4px solid #00ff00;
                            margin: 10px 0;
                        }
                        hr { border: 1px solid #00ff00; margin: 30px 0; }
                    </style>
                </head>
                <body>
                    <div style="text-align: center; margin-bottom: 30px;">
                        <h1>🧠 JARVIS AUTO-ADAPTATION TURBO</h1>
                        <p style="color: #00ffff;">Fiche Technique Complète - Système Opérationnel</p>
                        <p style="color: #ffff00;">Dernière mise à jour: ${new Date().toLocaleString('fr-FR')}</p>

                        <div style="margin: 20px 0;">
                            <a href="/status" style="background: linear-gradient(45deg, #003300, #006600); border: 1px solid #00ff00; color: #00ff00; padding: 10px 20px; margin: 0 10px; text-decoration: none; border-radius: 5px; display: inline-block;">📊 Status Système</a>
                            <a href="/adaptation-metrics" style="background: linear-gradient(45deg, #003333, #006666); border: 1px solid #00ffff; color: #00ffff; padding: 10px 20px; margin: 0 10px; text-decoration: none; border-radius: 5px; display: inline-block;">⚡ Métriques Live</a>
                            <a href="file:///Volumes/seagate/Louna_Electron_Latest/interface-jarvis-complete.html" style="background: linear-gradient(45deg, #330033, #660066); border: 1px solid #ff00ff; color: #ff00ff; padding: 10px 20px; margin: 0 10px; text-decoration: none; border-radius: 5px; display: inline-block;">🏠 Interface JARVIS</a>
                            <button onclick="window.print()" style="background: linear-gradient(45deg, #333300, #666600); border: 1px solid #ffff00; color: #ffff00; padding: 10px 20px; margin: 0 10px; border-radius: 5px; cursor: pointer;">🖨️ Imprimer</button>
                        </div>
                    </div>
                    <pre style="white-space: pre-wrap;">${ficheContent}</pre>
                    <div style="text-align: center; margin-top: 30px;">
                        <p style="color: #00ffff;">🔥 SYSTÈME AUTHENTIQUE - SANS SIMULATION 🔥</p>
                        <p style="color: #00ff00;">✅ CODE VIVANT ACTIF - ÉVOLUTION CONTINUE ✅</p>

                        <div style="margin: 20px 0; padding: 20px; border: 1px solid #00ff00; border-radius: 10px; background: rgba(0, 20, 0, 0.3);">
                            <h3 style="color: #00ffff; margin-bottom: 15px;">🔗 NAVIGATION RAPIDE</h3>
                            <a href="/status" style="background: linear-gradient(45deg, #003300, #006600); border: 1px solid #00ff00; color: #00ff00; padding: 8px 15px; margin: 5px; text-decoration: none; border-radius: 5px; display: inline-block; font-size: 12px;">📊 Status</a>
                            <a href="/adaptation-metrics" style="background: linear-gradient(45deg, #003333, #006666); border: 1px solid #00ffff; color: #00ffff; padding: 8px 15px; margin: 5px; text-decoration: none; border-radius: 5px; display: inline-block; font-size: 12px;">⚡ Métriques</a>
                            <a href="file:///Volumes/seagate/Louna_Electron_Latest/interface-jarvis-complete.html" style="background: linear-gradient(45deg, #330033, #660066); border: 1px solid #ff00ff; color: #ff00ff; padding: 8px 15px; margin: 5px; text-decoration: none; border-radius: 5px; display: inline-block; font-size: 12px;">🏠 Interface</a>
                            <a href="/v1/chat/completions" style="background: linear-gradient(45deg, #333300, #666600); border: 1px solid #ffff00; color: #ffff00; padding: 8px 15px; margin: 5px; text-decoration: none; border-radius: 5px; display: inline-block; font-size: 12px;">🤖 API Chat</a>
                        </div>
                    </div>
                </body>
                </html>
            `);
        } else {
            res.status(404).json({
                error: 'Fiche technique non trouvée',
                path: fichePath
            });
        }
    } catch (error) {
        res.status(500).json({
            error: 'Erreur lecture fiche technique',
            message: error.message
        });
    }
});

// Route page d'accueil JARVIS
app.get('/', (req, res) => {
    res.setHeader('Content-Type', 'text/html; charset=utf-8');
    res.send(`
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>🧠 JARVIS - Accueil Système Auto-Adaptation TURBO</title>
            <style>
                body {
                    font-family: 'Courier New', monospace;
                    background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
                    color: #00ff00;
                    padding: 20px;
                    line-height: 1.6;
                    min-height: 100vh;
                }
                h1 { color: #00ffff; text-shadow: 0 0 20px #00ffff; text-align: center; font-size: 2.5em; }
                h2 { color: #00ffff; text-shadow: 0 0 10px #00ffff; }
                .welcome-panel {
                    text-align: center;
                    margin: 30px 0;
                    padding: 30px;
                    border: 2px solid #00ff00;
                    border-radius: 15px;
                    background: rgba(0, 20, 0, 0.3);
                    box-shadow: 0 0 30px rgba(0, 255, 0, 0.3);
                }
                .nav-grid {
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                    gap: 20px;
                    margin: 30px 0;
                }
                .nav-card {
                    background: rgba(0, 20, 0, 0.3);
                    border: 1px solid #00ff00;
                    border-radius: 10px;
                    padding: 20px;
                    text-align: center;
                    transition: all 0.3s ease;
                    cursor: pointer;
                }
                .nav-card:hover {
                    box-shadow: 0 0 20px rgba(0, 255, 0, 0.5);
                    transform: translateY(-5px);
                }
                .nav-card h3 { color: #00ffff; margin-bottom: 15px; }
                .nav-card p { color: #80ff80; margin-bottom: 15px; }
                .btn {
                    background: linear-gradient(45deg, #003300, #006600);
                    border: 1px solid #00ff00;
                    color: #00ff00;
                    padding: 12px 25px;
                    text-decoration: none;
                    border-radius: 5px;
                    display: inline-block;
                    transition: all 0.3s ease;
                    font-family: 'Courier New', monospace;
                    font-weight: bold;
                }
                .btn:hover { box-shadow: 0 0 15px #00ff00; transform: scale(1.05); }
                .btn-metrics { border-color: #00ffff; color: #00ffff; background: linear-gradient(45deg, #003333, #006666); }
                .btn-fiche { border-color: #ff00ff; color: #ff00ff; background: linear-gradient(45deg, #330033, #660066); }
                .btn-interface { border-color: #ffff00; color: #ffff00; background: linear-gradient(45deg, #333300, #666600); }
                .btn-health { border-color: #00ff88; color: #00ff88; background: linear-gradient(45deg, #003322, #006644); }
                .status-bar {
                    background: rgba(0, 0, 0, 0.8);
                    border: 1px solid #00ff00;
                    border-radius: 10px;
                    padding: 15px;
                    margin: 20px 0;
                    display: flex;
                    justify-content: space-around;
                    flex-wrap: wrap;
                }
                .status-item {
                    text-align: center;
                    margin: 5px;
                }
                .status-label { color: #80ff80; font-size: 12px; }
                .status-value { color: #ffff00; font-weight: bold; }
                .footer {
                    text-align: center;
                    margin-top: 50px;
                    padding: 20px;
                    border-top: 1px solid #00ff00;
                    color: #80ff80;
                }
            </style>
        </head>
        <body>
            <div class="welcome-panel">
                <h1>🧠 JARVIS AUTO-ADAPTATION TURBO</h1>
                <p style="color: #00ffff; font-size: 1.2em;">Système d'Intelligence Artificielle Authentique</p>
                <p style="color: #ffff00;">Connexion Directe DeepSeek R1 8B - Code Vivant Évolutif</p>
                <p style="color: #ff00ff;">SANS OLLAMA - Branchement Direct Neural Engine M4</p>
            </div>

            <div class="status-bar">
                <div class="status-item">
                    <div class="status-label">🌡️ Température</div>
                    <div class="status-value" id="temp">--°C</div>
                </div>
                <div class="status-item">
                    <div class="status-label">⚡ TURBO</div>
                    <div class="status-value" id="turbo">--/10</div>
                </div>
                <div class="status-item">
                    <div class="status-label">🧬 Évolution</div>
                    <div class="status-value" id="cycles">-- cycles</div>
                </div>
                <div class="status-item">
                    <div class="status-label">💾 Mémoire</div>
                    <div class="status-value" id="memory">--%</div>
                </div>
                <div class="status-item">
                    <div class="status-label">🖥️ CPU</div>
                    <div class="status-value" id="cpu">-- cores</div>
                </div>
            </div>

            <div class="nav-grid">
                <div class="nav-card" onclick="window.open('file:///Volumes/seagate/Louna_Electron_Latest/interface-jarvis-complete.html', '_blank')">
                    <h3>🏠 Interface JARVIS</h3>
                    <p>Interface principale de chat avec JARVIS<br>Mémoire thermique intégrée</p>
                    <a href="file:///Volumes/seagate/Louna_Electron_Latest/interface-jarvis-complete.html" class="btn btn-interface">Accéder</a>
                </div>

                <div class="nav-card" onclick="window.open('/status', '_blank')">
                    <h3>📊 Status Système</h3>
                    <p>État du serveur et du modèle<br>Informations techniques</p>
                    <a href="/status" class="btn">Consulter</a>
                </div>

                <div class="nav-card" onclick="window.open('/adaptation-metrics', '_blank')">
                    <h3>⚡ Métriques TURBO</h3>
                    <p>Auto-adaptation en temps réel<br>Code vivant évolutif</p>
                    <a href="/adaptation-metrics" class="btn btn-metrics">Surveiller</a>
                </div>

                <div class="nav-card" onclick="window.open('/fiche-technique', '_blank')">
                    <h3>📋 Fiche Technique</h3>
                    <p>Documentation complète<br>Architecture et spécifications</p>
                    <a href="/fiche-technique" class="btn btn-fiche">Lire</a>
                </div>

                <div class="nav-card" onclick="window.open('/health', '_blank')">
                    <h3>💚 Health Check</h3>
                    <p>Santé du système<br>Diagnostic rapide</p>
                    <a href="/health" class="btn btn-health">Vérifier</a>
                </div>

                <div class="nav-card" onclick="window.open('/v1/chat/completions', '_blank')">
                    <h3>🤖 API Chat</h3>
                    <p>Endpoint API REST<br>Intégration développeurs</p>
                    <a href="/v1/chat/completions" class="btn">Tester</a>
                </div>
            </div>

            <div class="footer">
                <p>🔥 SYSTÈME AUTHENTIQUE - SANS SIMULATION 🔥</p>
                <p>✅ CODE VIVANT ACTIF - ÉVOLUTION CONTINUE ✅</p>
                <p>Créé par Jean-Luc PASSAVE avec Claude Sonnet 4</p>
                <p>Dernière mise à jour: ${new Date().toLocaleString('fr-FR')}</p>
            </div>

            <script>
                // Mise à jour des métriques
                async function updateMetrics() {
                    try {
                        const response = await fetch('/adaptation-metrics', {
                            headers: { 'Accept': 'application/json' }
                        });
                        const metrics = await response.json();

                        if (metrics.system_health) {
                            document.getElementById('temp').textContent =
                                (metrics.system_health.thermal_temperature?.toFixed(1) || '--') + '°C';
                            document.getElementById('memory').textContent =
                                ((metrics.system_health.memory_pressure || 0) * 100).toFixed(1) + '%';
                        }

                        if (metrics.turbo_performance) {
                            document.getElementById('turbo').textContent =
                                \`\${metrics.turbo_performance.turbo_level || 0}/\${metrics.turbo_performance.max_turbo_level || 10}\`;
                        }

                        if (metrics.living_code) {
                            document.getElementById('cycles').textContent =
                                (metrics.living_code.evolution_cycles || 0) + ' cycles';
                        }

                        if (metrics.machine_optimization) {
                            document.getElementById('cpu').textContent =
                                (metrics.machine_optimization.cpu_cores || '--') + ' cores';
                        }

                    } catch (error) {
                        console.error('Erreur mise à jour métriques:', error);
                    }
                }

                // Mise à jour initiale et périodique
                updateMetrics();
                setInterval(updateMetrics, 5000);
            </script>
        </body>
        </html>
    `);
});

// Route de santé
app.get('/health', (req, res) => {
    const healthData = {
        status: 'healthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        jarvis_system: deepseekConnector ? 'operational' : 'initializing'
    };

    // Si c'est une requête JSON (API)
    if (req.headers.accept && req.headers.accept.includes('application/json')) {
        res.json(healthData);
        return;
    }

    // Interface HTML pour le navigateur
    res.setHeader('Content-Type', 'text/html; charset=utf-8');
    res.send(`
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>JARVIS - Health Check</title>
            <style>
                body {
                    font-family: 'Courier New', monospace;
                    background: #0a0a0a;
                    color: #00ff00;
                    padding: 20px;
                    line-height: 1.6;
                }
                h1 { color: #00ffff; text-shadow: 0 0 10px #00ffff; text-align: center; }
                .nav-buttons {
                    text-align: center;
                    margin: 20px 0;
                    padding: 20px;
                    border: 1px solid #00ff00;
                    border-radius: 10px;
                    background: rgba(0, 20, 0, 0.3);
                }
                .btn {
                    background: linear-gradient(45deg, #003300, #006600);
                    border: 1px solid #00ff00;
                    color: #00ff00;
                    padding: 10px 20px;
                    margin: 5px;
                    text-decoration: none;
                    border-radius: 5px;
                    display: inline-block;
                    transition: all 0.3s ease;
                }
                .btn:hover { box-shadow: 0 0 15px #00ff00; }
                .health-ok { color: #00ff00; }
                .health-warning { color: #ffff00; }
                .health-critical { color: #ff0000; }
                pre {
                    background: #001100;
                    padding: 15px;
                    border-radius: 5px;
                    border: 1px solid #00ff00;
                    overflow-x: auto;
                }
            </style>
        </head>
        <body>
            <h1>💚 JARVIS - HEALTH CHECK</h1>

            <div class="nav-buttons">
                <h3 style="color: #00ffff; margin-bottom: 15px;">🔗 NAVIGATION JARVIS</h3>
                <a href="/" class="btn">🏠 Accueil</a>
                <a href="/status" class="btn">📊 Status</a>
                <a href="/adaptation-metrics" class="btn">⚡ Métriques</a>
                <a href="/fiche-technique" class="btn">📋 Fiche Technique</a>
                <button onclick="location.reload()" class="btn">🔄 Actualiser</button>
            </div>

            <h2>🏥 État de Santé Système</h2>
            <p>Status: <span class="health-ok">✅ ${healthData.status.toUpperCase()}</span></p>
            <p>JARVIS: <span class="health-ok">✅ ${healthData.jarvis_system.toUpperCase()}</span></p>
            <p>Uptime: <span class="health-ok">${Math.floor(healthData.uptime)} secondes</span></p>
            <p>Timestamp: <span class="health-ok">${new Date(healthData.timestamp).toLocaleString('fr-FR')}</span></p>

            <h2>💾 Utilisation Mémoire</h2>
            <pre>${JSON.stringify(healthData.memory, null, 2)}</pre>

            <h2>📊 Données Complètes</h2>
            <pre>${JSON.stringify(healthData, null, 2)}</pre>

            <div style="text-align: center; margin-top: 30px; padding: 20px; border: 1px solid #00ff00; border-radius: 10px; background: rgba(0, 20, 0, 0.3);">
                <p style="color: #00ffff;">🔥 SYSTÈME EN BONNE SANTÉ 🔥</p>
                <p style="color: #00ff00;">✅ TOUS LES SYSTÈMES OPÉRATIONNELS ✅</p>
            </div>

            <script>
                // Auto-actualisation toutes les 30 secondes
                setTimeout(() => location.reload(), 30000);
            </script>
        </body>
        </html>
    `);
});

// Démarrage du serveur
async function startServer() {
    // Initialiser DeepSeek
    await initializeDeepSeek();
    
    // Démarrer le serveur
    app.listen(PORT, () => {
        console.log('');
        console.log('🎉 === SERVEUR DEEPSEEK R1 8B DÉMARRÉ ===');
        console.log(`🌐 URL: http://localhost:${PORT}`);
        console.log(`🔗 API: http://localhost:${PORT}/v1/chat/completions`);
        console.log(`📊 Statut: http://localhost:${PORT}/status`);
        console.log('');
        console.log('🎨 Interface Claude peut maintenant se connecter !');
        console.log('🔌 Modèle DeepSeek R1 8B prêt pour les requêtes');
        console.log('');
    });
}

// Gestion des erreurs
process.on('uncaughtException', (error) => {
    console.error('❌ Erreur non gérée:', error);
});

process.on('unhandledRejection', (reason, promise) => {
    console.error('❌ Promesse rejetée:', reason);
});

// Démarrage
startServer().catch(error => {
    console.error('❌ Erreur démarrage serveur:', error);
    process.exit(1);
});
