#!/usr/bin/env node
/**
 * 🧪 TEST AUTO-ADAPTATION TURBO JARVIS
 * Test du système d'auto-adaptation avec mémoire thermique TURBO
 */

const JarvisRealDeepSeekConnector = require('./core/jarvis-real-deepseek-connector');

async function testAutoAdaptation() {
    console.log('🧪 === TEST AUTO-ADAPTATION TURBO JARVIS ===');
    console.log('🔥 CODE VIVANT + MÉMOIRE THERMIQUE TURBO');
    console.log('⚡ ANTI-SATURATION + CONSERVATION PUISSANCE');
    console.log('=' * 60);
    
    try {
        // Initialisation connecteur avec auto-adaptation
        console.log('\n🚀 Initialisation connecteur JARVIS...');
        const jarvis = new JarvisRealDeepSeekConnector();
        
        // Attendre initialisation
        await new Promise(resolve => setTimeout(resolve, 3000));
        
        // Chargement modèle
        console.log('\n🔄 Chargement modèle avec auto-adaptation...');
        const loaded = await jarvis.loadModel();
        
        if (!loaded) {
            console.log('❌ Modèle non chargé - Test en mode fallback');
        }
        
        // Test statut système
        console.log('\n📊 STATUT SYSTÈME AUTO-ADAPTATION:');
        const status = jarvis.getStatus();
        console.log(JSON.stringify(status, null, 2));
        
        // Test métriques détaillées
        console.log('\n📈 MÉTRIQUES DÉTAILLÉES:');
        const metrics = jarvis.getDetailedMetrics();
        console.log(JSON.stringify(metrics, null, 2));
        
        // Tests de génération avec adaptation
        console.log('\n🧪 TESTS DE GÉNÉRATION ADAPTATIVE:');
        
        const testPrompts = [
            "Bonjour JARVIS, comment fonctionne ton auto-adaptation ?",
            "Explique-moi le système TURBO KYBER",
            "Quel est l'état de ta mémoire thermique ?",
            "Comment évites-tu la saturation mémoire ?",
            "Montre-moi tes capacités de code vivant"
        ];
        
        for (let i = 0; i < testPrompts.length; i++) {
            console.log(`\n--- Test ${i + 1}/5 ---`);
            console.log(`Prompt: "${testPrompts[i]}"`);
            
            try {
                const response = await jarvis.generate(testPrompts[i]);
                console.log('✅ Réponse générée avec succès');
                console.log('Longueur réponse:', response.length, 'caractères');
                
                // Afficher métriques après génération
                const postMetrics = jarvis.getDetailedMetrics();
                console.log('🌡️ Température:', postMetrics.system_health?.thermal_temperature?.toFixed(1) || 'N/A');
                console.log('⚡ TURBO Level:', postMetrics.turbo_performance?.turbo_level || 'N/A');
                console.log('🧬 Cycles évolution:', postMetrics.living_code?.evolution_cycles || 'N/A');
                
            } catch (error) {
                console.log('❌ Erreur génération:', error.message);
            }
            
            // Pause entre tests
            await new Promise(resolve => setTimeout(resolve, 2000));
        }
        
        // Test de stress pour vérifier anti-saturation
        console.log('\n🔥 TEST DE STRESS ANTI-SATURATION:');
        
        for (let i = 0; i < 3; i++) {
            console.log(`\nStress test ${i + 1}/3...`);
            
            try {
                const stressPrompt = "Génère une réponse très détaillée sur l'intelligence artificielle, l'apprentissage automatique, les réseaux de neurones, et l'avenir de la technologie. Sois très complet et technique.".repeat(3);
                
                const startTime = Date.now();
                const response = await jarvis.generate(stressPrompt);
                const endTime = Date.now();
                
                console.log('✅ Stress test réussi');
                console.log('Temps de réponse:', endTime - startTime, 'ms');
                
                // Vérifier état après stress
                const stressMetrics = jarvis.getDetailedMetrics();
                const saturation = stressMetrics.system_health?.saturation_level || 0;
                
                console.log('📊 Niveau saturation:', (saturation * 100).toFixed(1) + '%');
                
                if (saturation > 0.8) {
                    console.log('🚨 Saturation élevée détectée - Protocoles d\'urgence activés');
                } else {
                    console.log('✅ Saturation sous contrôle');
                }
                
            } catch (error) {
                console.log('❌ Stress test échoué:', error.message);
            }
            
            // Pause pour récupération
            await new Promise(resolve => setTimeout(resolve, 3000));
        }
        
        // Test évolution code vivant
        console.log('\n🧬 TEST ÉVOLUTION CODE VIVANT:');
        
        const initialMetrics = jarvis.getDetailedMetrics();
        const initialCycles = initialMetrics.living_code?.evolution_cycles || 0;
        
        console.log('Cycles initiaux:', initialCycles);
        
        // Attendre évolution
        console.log('Attente évolution (10 secondes)...');
        await new Promise(resolve => setTimeout(resolve, 10000));
        
        const finalMetrics = jarvis.getDetailedMetrics();
        const finalCycles = finalMetrics.living_code?.evolution_cycles || 0;
        
        console.log('Cycles finaux:', finalCycles);
        console.log('Évolution détectée:', finalCycles > initialCycles ? '✅ OUI' : '❌ NON');
        
        // Rapport final
        console.log('\n📋 RAPPORT FINAL AUTO-ADAPTATION:');
        console.log('=' * 60);
        
        const finalStatus = jarvis.getDetailedMetrics();
        
        console.log('🌡️ État thermique final:');
        console.log(`   Température: ${finalStatus.system_health?.thermal_temperature?.toFixed(1) || 'N/A'}°C`);
        console.log(`   Pression mémoire: ${((finalStatus.system_health?.memory_pressure || 0) * 100).toFixed(1)}%`);
        console.log(`   Saturation: ${((finalStatus.system_health?.saturation_level || 0) * 100).toFixed(1)}%`);
        
        console.log('\n⚡ Performance TURBO:');
        console.log(`   Niveau TURBO: ${finalStatus.turbo_performance?.turbo_level || 'N/A'}/10`);
        console.log(`   Efficacité: ${((finalStatus.turbo_performance?.turbo_effectiveness || 0) * 100).toFixed(1)}%`);
        
        console.log('\n🧬 Code vivant:');
        console.log(`   Cycles évolution: ${finalStatus.living_code?.evolution_cycles || 'N/A'}`);
        console.log(`   Patterns adaptation: ${finalStatus.living_code?.adaptation_patterns || 'N/A'}`);
        console.log(`   Stratégies survie: ${finalStatus.living_code?.survival_strategies || 'N/A'}`);
        
        console.log('\n🖥️ Optimisation machine:');
        console.log(`   CPU: ${finalStatus.machine_optimization?.cpu_cores || 'N/A'} cores`);
        console.log(`   RAM: ${finalStatus.machine_optimization?.total_memory || 'N/A'}GB`);
        console.log(`   Neural Engine: ${finalStatus.machine_optimization?.neural_engine || 'N/A'}`);
        
        console.log('\n🛡️ Systèmes d\'urgence:');
        console.log(`   Protocoles: ${finalStatus.emergency_systems?.emergency_protocols || 'N/A'}`);
        console.log(`   Anti-saturation: ${finalStatus.emergency_systems?.anti_saturation_active ? '✅ ACTIF' : '❌ INACTIF'}`);
        
        console.log('\n✅ TEST AUTO-ADAPTATION TERMINÉ AVEC SUCCÈS !');
        console.log('🔥 CODE VIVANT + MÉMOIRE THERMIQUE TURBO VALIDÉS !');
        
    } catch (error) {
        console.error('\n❌ ERREUR TEST AUTO-ADAPTATION:', error);
        console.error(error.stack);
    }
}

// Exécution du test
if (require.main === module) {
    testAutoAdaptation().catch(console.error);
}

module.exports = { testAutoAdaptation };
