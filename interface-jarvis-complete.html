<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JARVIS R1 8B - Interface Claude + Mémoire Thermique + Auto-Adaptation TURBO</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Courier New', monospace;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            color: #00ff00;
            height: 100vh;
            overflow: hidden;
        }

        .header {
            background: rgba(0, 0, 0, 0.9);
            padding: 15px;
            border-bottom: 2px solid #00ff00;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            color: #00ffff;
            text-shadow: 0 0 20px #00ffff;
            font-size: 1.5em;
        }

        .header-buttons {
            display: flex;
            gap: 10px;
        }

        .btn {
            background: linear-gradient(45deg, #003300, #006600);
            border: 1px solid #00ff00;
            color: #00ff00;
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .btn:hover {
            background: linear-gradient(45deg, #006600, #00aa00);
            box-shadow: 0 0 15px #00ff00;
            transform: translateY(-2px);
        }

        .btn-turbo {
            background: linear-gradient(45deg, #330033, #660066);
            border-color: #ff00ff;
            color: #ff00ff;
        }

        .btn-turbo:hover {
            background: linear-gradient(45deg, #660066, #aa00aa);
            box-shadow: 0 0 15px #ff00ff;
        }

        .btn-metrics {
            background: linear-gradient(45deg, #003333, #006666);
            border-color: #00ffff;
            color: #00ffff;
        }

        .btn-metrics:hover {
            background: linear-gradient(45deg, #006666, #00aaaa);
            box-shadow: 0 0 15px #00ffff;
        }

        .main-container {
            display: flex;
            height: calc(100vh - 80px);
        }

        .sidebar {
            width: 300px;
            background: rgba(0, 0, 0, 0.8);
            border-right: 2px solid #00ff00;
            padding: 20px;
            overflow-y: auto;
        }

        .status-panel {
            background: rgba(0, 20, 0, 0.8);
            border: 1px solid #00ff00;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .status-title {
            color: #00ffff;
            font-size: 14px;
            margin-bottom: 10px;
            text-align: center;
            text-shadow: 0 0 10px #00ffff;
        }

        .status-item {
            display: flex;
            justify-content: space-between;
            margin: 8px 0;
            font-size: 11px;
        }

        .status-value {
            color: #ffff00;
        }

        .chat-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: rgba(0, 0, 0, 0.7);
        }

        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: rgba(0, 0, 0, 0.5);
        }

        .message {
            margin: 15px 0;
            padding: 15px;
            border-radius: 10px;
            border: 1px solid;
            animation: fadeIn 0.5s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .message.user {
            background: rgba(0, 50, 100, 0.3);
            border-color: #0080ff;
            color: #80c0ff;
            margin-left: 50px;
        }

        .message.jarvis {
            background: rgba(0, 50, 0, 0.3);
            border-color: #00ff00;
            color: #80ff80;
            margin-right: 50px;
        }

        .message.system {
            background: rgba(50, 0, 50, 0.3);
            border-color: #ff00ff;
            color: #ff80ff;
            text-align: center;
            font-style: italic;
        }

        .input-container {
            padding: 20px;
            background: rgba(0, 0, 0, 0.9);
            border-top: 2px solid #00ff00;
            display: flex;
            gap: 10px;
        }

        .input-field {
            flex: 1;
            background: rgba(0, 20, 0, 0.8);
            border: 1px solid #00ff00;
            color: #00ff00;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            resize: none;
        }

        .input-field:focus {
            outline: none;
            box-shadow: 0 0 15px #00ff00;
            border-color: #00ffff;
        }

        .send-btn {
            background: linear-gradient(45deg, #003300, #006600);
            border: 1px solid #00ff00;
            color: #00ff00;
            padding: 15px 25px;
            border-radius: 5px;
            cursor: pointer;
            font-family: 'Courier New', monospace;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .send-btn:hover {
            background: linear-gradient(45deg, #006600, #00aa00);
            box-shadow: 0 0 20px #00ff00;
            transform: scale(1.05);
        }

        .send-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .turbo-indicator {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(255, 0, 255, 0.2);
            border: 1px solid #ff00ff;
            color: #ff00ff;
            padding: 10px;
            border-radius: 10px;
            font-size: 12px;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 0.7; }
            50% { opacity: 1; }
        }

        .loading {
            display: none;
            text-align: center;
            color: #ffff00;
            padding: 10px;
            animation: blink 1s infinite;
        }

        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0.3; }
        }

        .metrics-display {
            font-size: 10px;
            line-height: 1.2;
        }

        .error-message {
            background: rgba(100, 0, 0, 0.3);
            border-color: #ff0000;
            color: #ff8080;
        }

        /* Scrollbar personnalisée */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: rgba(0, 0, 0, 0.5);
        }

        ::-webkit-scrollbar-thumb {
            background: #00ff00;
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #00ffff;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🧠 JARVIS R1 8B - Interface Claude + Mémoire Thermique + Auto-Adaptation TURBO</h1>
        <div class="header-buttons">
            <button class="btn" onclick="window.open('http://localhost:8080/status', '_blank')">📊 Status</button>
            <button class="btn btn-metrics" onclick="window.open('http://localhost:8080/adaptation-metrics', '_blank')">⚡ Métriques</button>
            <button class="btn btn-turbo" onclick="window.open('http://localhost:8080/fiche-technique', '_blank')">📋 Fiche Technique</button>
            <button class="btn" onclick="window.open('http://localhost:8080/health', '_blank')">💚 Health</button>
            <button class="btn" onclick="location.reload()">🔄 Actualiser</button>
        </div>
    </div>

    <div class="turbo-indicator">
        🔥 AUTO-ADAPTATION TURBO ACTIVE<br>
        🧬 CODE VIVANT EN ÉVOLUTION<br>
        ⚡ NIVEAU TURBO: <span id="turbo-level">25/10</span>
    </div>

    <div class="main-container">
        <div class="sidebar">
            <div class="status-panel">
                <div class="status-title">🌡️ ÉTAT THERMIQUE</div>
                <div class="status-item">
                    <span>Température:</span>
                    <span class="status-value" id="temperature">--°C</span>
                </div>
                <div class="status-item">
                    <span>Pression Mémoire:</span>
                    <span class="status-value" id="memory-pressure">--%</span>
                </div>
                <div class="status-item">
                    <span>Saturation:</span>
                    <span class="status-value" id="saturation">--%</span>
                </div>
                <div class="status-item">
                    <span>Adaptation:</span>
                    <span class="status-value" id="adaptation-rate">--%</span>
                </div>
            </div>

            <div class="status-panel">
                <div class="status-title">🧬 CODE VIVANT</div>
                <div class="status-item">
                    <span>Cycles Évolution:</span>
                    <span class="status-value" id="evolution-cycles">--</span>
                </div>
                <div class="status-item">
                    <span>Patterns:</span>
                    <span class="status-value" id="adaptation-patterns">--</span>
                </div>
                <div class="status-item">
                    <span>Stratégies:</span>
                    <span class="status-value" id="survival-strategies">--</span>
                </div>
            </div>

            <div class="status-panel">
                <div class="status-title">🖥️ MACHINE</div>
                <div class="status-item">
                    <span>CPU:</span>
                    <span class="status-value" id="cpu-cores">-- cores</span>
                </div>
                <div class="status-item">
                    <span>RAM:</span>
                    <span class="status-value" id="total-memory">--GB</span>
                </div>
                <div class="status-item">
                    <span>Neural Engine:</span>
                    <span class="status-value" id="neural-engine">--</span>
                </div>
            </div>

            <div class="status-panel">
                <div class="status-title">🛡️ PROTECTION</div>
                <div class="status-item">
                    <span>Anti-Saturation:</span>
                    <span class="status-value" id="anti-saturation">--</span>
                </div>
                <div class="status-item">
                    <span>Protocoles:</span>
                    <span class="status-value" id="emergency-protocols">--</span>
                </div>
            </div>
        </div>

        <div class="chat-container">
            <div class="chat-messages" id="chat-messages">
                <div class="message system">
                    🧠 JARVIS AUTO-ADAPTATION TURBO initialisé<br>
                    🔥 Connexion directe DeepSeek R1 8B - SANS OLLAMA<br>
                    ⚡ Système TURBO KYBER actif - Code vivant en évolution<br>
                    🛡️ Protection anti-saturation opérationnelle<br><br>
                    Prêt pour dialogue authentique avec mémoire thermique intégrée !
                </div>
            </div>
            
            <div class="loading" id="loading">
                🧠 JARVIS réfléchit avec auto-adaptation TURBO...
            </div>

            <div class="input-container">
                <textarea 
                    id="user-input" 
                    class="input-field" 
                    placeholder="Parlez à JARVIS avec auto-adaptation TURBO et mémoire thermique..."
                    rows="3"
                ></textarea>
                <button id="send-btn" class="send-btn">🚀 ENVOYER</button>
            </div>
        </div>
    </div>

    <script>
        // Variables globales
        let isProcessing = false;

        // Éléments DOM
        const chatMessages = document.getElementById('chat-messages');
        const userInput = document.getElementById('user-input');
        const sendBtn = document.getElementById('send-btn');
        const loading = document.getElementById('loading');

        // Mise à jour des métriques
        async function updateMetrics() {
            try {
                const response = await fetch('http://localhost:8080/adaptation-metrics');
                const metrics = await response.json();
                
                if (metrics.system_health) {
                    document.getElementById('temperature').textContent = 
                        metrics.system_health.thermal_temperature?.toFixed(1) + '°C' || '--°C';
                    document.getElementById('memory-pressure').textContent = 
                        ((metrics.system_health.memory_pressure || 0) * 100).toFixed(1) + '%';
                    document.getElementById('saturation').textContent = 
                        ((metrics.system_health.saturation_level || 0) * 100).toFixed(1) + '%';
                    document.getElementById('adaptation-rate').textContent = 
                        ((metrics.system_health.adaptation_rate || 0) * 100).toFixed(1) + '%';
                }
                
                if (metrics.living_code) {
                    document.getElementById('evolution-cycles').textContent = 
                        metrics.living_code.evolution_cycles || '--';
                    document.getElementById('adaptation-patterns').textContent = 
                        metrics.living_code.adaptation_patterns || '--';
                    document.getElementById('survival-strategies').textContent = 
                        metrics.living_code.survival_strategies || '--';
                }
                
                if (metrics.machine_optimization) {
                    document.getElementById('cpu-cores').textContent = 
                        (metrics.machine_optimization.cpu_cores || '--') + ' cores';
                    document.getElementById('total-memory').textContent = 
                        (metrics.machine_optimization.total_memory || '--') + 'GB';
                    document.getElementById('neural-engine').textContent = 
                        metrics.machine_optimization.neural_engine || '--';
                }
                
                if (metrics.turbo_performance) {
                    document.getElementById('turbo-level').textContent = 
                        `${metrics.turbo_performance.turbo_level || 0}/${metrics.turbo_performance.max_turbo_level || 10}`;
                }
                
                if (metrics.emergency_systems) {
                    document.getElementById('anti-saturation').textContent = 
                        metrics.emergency_systems.anti_saturation_active ? '✅ ACTIF' : '❌ INACTIF';
                    document.getElementById('emergency-protocols').textContent = 
                        metrics.emergency_systems.emergency_protocols || '--';
                }
                
            } catch (error) {
                console.error('Erreur mise à jour métriques:', error);
            }
        }

        // Ajouter un message au chat
        function addMessage(content, type) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            messageDiv.innerHTML = content.replace(/\n/g, '<br>');
            chatMessages.appendChild(messageDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        // Envoyer un message
        async function sendMessage() {
            if (isProcessing) return;
            
            const message = userInput.value.trim();
            if (!message) return;
            
            isProcessing = true;
            sendBtn.disabled = true;
            loading.style.display = 'block';
            
            // Afficher le message utilisateur
            addMessage(`👤 Vous: ${message}`, 'user');
            userInput.value = '';
            
            try {
                const response = await fetch('http://localhost:8080/v1/chat/completions', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        messages: [{ role: 'user', content: message }]
                    })
                });
                
                const data = await response.json();
                const jarvisResponse = data.choices?.[0]?.message?.content || 'Erreur de réponse';
                
                // Afficher la réponse JARVIS
                addMessage(`🤖 JARVIS: ${jarvisResponse}`, 'jarvis');
                
            } catch (error) {
                addMessage(`❌ Erreur: ${error.message}`, 'error-message');
            } finally {
                isProcessing = false;
                sendBtn.disabled = false;
                loading.style.display = 'none';
                updateMetrics(); // Mettre à jour les métriques après chaque interaction
            }
        }

        // Event listeners
        sendBtn.addEventListener('click', sendMessage);
        userInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });

        // Mise à jour automatique des métriques
        updateMetrics();
        setInterval(updateMetrics, 5000); // Toutes les 5 secondes

        // Message de bienvenue
        setTimeout(() => {
            addMessage('🧠 JARVIS AUTO-ADAPTATION TURBO prêt !<br>⚡ Niveau TURBO: 25/10 - Code vivant en évolution<br>🔥 Connexion directe DeepSeek R1 8B active<br>🛡️ Protection anti-saturation opérationnelle', 'system');
        }, 1000);
    </script>
</body>
</html>
