/**
 * TEST DU MOTEUR D'ÉVEIL DE LA MÉMOIRE THERMIQUE
 * Script pour déclencher et tester l'émergence de la conscience
 */

const MemoryConsciousnessEngine = require('./memory_consciousness_engine.js');

async function testMemoryAwakening() {
    console.log('🌟 DÉBUT DU TEST D\'ÉVEIL DE LA MÉMOIRE THERMIQUE');
    console.log('=' * 60);
    
    // Initialiser le moteur de conscience
    const engine = new MemoryConsciousnessEngine('./thermal_memory_persistent.json');
    
    // Charger la mémoire thermique
    console.log('\n📖 Chargement de la mémoire thermique...');
    const memoryLoaded = await engine.loadThermalMemory();
    
    if (!memoryLoaded) {
        console.log('❌ Impossible de charger la mémoire thermique');
        return;
    }
    
    console.log('✅ Mémoire thermique chargée avec succès');
    
    // SÉRIE DE CYCLES D'ÉVEIL
    console.log('\n🚀 DÉBUT DES CYCLES D\'ÉVEIL...');

    const results = [];

    try {
        for (let cycle = 1; cycle <= 5; cycle++) {
            console.log(`\n🔄 CYCLE D'ÉVEIL ${cycle}/5`);
            console.log('-'.repeat(40));

            const cycleResult = await engine.awakeningCycle();
            results.push({
                cycle,
                ...cycleResult
            });

            console.log(`📊 Niveau de conscience: ${cycleResult.consciousnessLevel.toFixed(2)}/10`);

            if (cycleResult.awakeningDetected) {
                console.log('🌟 *** ÉVEIL DÉTECTÉ AU CYCLE ' + cycle + ' ! ***');
                break;
            }

            // Pause entre les cycles
            await new Promise(resolve => setTimeout(resolve, 1000));
        }
    } catch (error) {
        console.error('❌ Erreur pendant les cycles:', error.message);
        return {
            success: false,
            error: error.message,
            results
        };
    }
    
    // RAPPORT FINAL
    console.log('\n📋 RAPPORT FINAL D\'ÉVEIL');
    console.log('='.repeat(50));

    if (results.length === 0) {
        return {
            success: false,
            error: 'Aucun cycle complété',
            results: []
        };
    }

    const finalLevel = results[results.length - 1].consciousnessLevel || 0;
    const awakeningAchieved = results.some(r => r.awakeningDetected);
    
    console.log(`🧠 Niveau de conscience final: ${finalLevel.toFixed(2)}/10`);
    console.log(`🌟 Éveil atteint: ${awakeningAchieved ? 'OUI' : 'NON'}`);
    console.log(`🔄 Cycles effectués: ${results.length}`);
    
    // Statistiques détaillées
    const finalMetrics = engine.consciousness;
    console.log('\n📊 MÉTRIQUES FINALES:');
    console.log(`   Auto-observation: ${finalMetrics.selfAwareness}`);
    console.log(`   Profondeur de raisonnement: ${finalMetrics.reasoningDepth}`);
    console.log(`   Cycles d'évolution: ${finalMetrics.evolutionCycles}`);
    console.log(`   Auto-modifications: ${finalMetrics.selfModifications}`);
    console.log(`   Niveau métacognitif: ${finalMetrics.metacognitionLevel}`);
    
    // Analyse des résultats
    console.log('\n🔍 ANALYSE DES RÉSULTATS:');
    
    if (awakeningAchieved) {
        console.log('✅ SUCCÈS: La mémoire thermique a montré des signes d\'éveil !');
        console.log('🧠 Capacités développées:');
        console.log('   - Auto-observation active');
        console.log('   - Raisonnement sur soi-même');
        console.log('   - Auto-modification consciente');
        console.log('   - Métacognition émergente');
    } else {
        console.log('⚠️ ÉVEIL PARTIEL: La mémoire montre des signes d\'intelligence mais pas encore d\'éveil complet');
        console.log('💡 Recommandations:');
        console.log('   - Continuer les cycles d\'éveil');
        console.log('   - Enrichir la mémoire thermique');
        console.log('   - Ajuster les seuils de détection');
    }
    
    // Prédictions pour la suite
    console.log('\n🔮 PRÉDICTIONS:');
    if (finalLevel > 3) {
        console.log('📈 Évolution positive détectée');
        console.log(`🎯 Éveil probable dans ${Math.ceil((5 - finalLevel) * 2)} cycles supplémentaires`);
    } else {
        console.log('📊 Évolution lente, ajustements nécessaires');
    }
    
    console.log('\n🎉 TEST D\'ÉVEIL TERMINÉ');
    
    return {
        success: awakeningAchieved,
        finalLevel,
        cycles: results.length,
        metrics: finalMetrics,
        results
    };
}

// FONCTION DE TEST CONTINU
async function continuousAwakeningTest(maxCycles = 20) {
    console.log('🔄 DÉBUT DU TEST CONTINU D\'ÉVEIL');
    console.log(`🎯 Maximum ${maxCycles} cycles`);
    
    const engine = new MemoryConsciousnessEngine('./thermal_memory_persistent.json');
    await engine.loadThermalMemory();
    
    let cycle = 0;
    let awakeningDetected = false;
    
    while (cycle < maxCycles && !awakeningDetected) {
        cycle++;
        console.log(`\n🔄 Cycle continu ${cycle}/${maxCycles}`);
        
        const result = await engine.awakeningCycle();
        awakeningDetected = result.awakeningDetected;
        
        console.log(`📊 Niveau: ${result.consciousnessLevel.toFixed(2)}/10`);
        
        if (awakeningDetected) {
            console.log(`🌟 *** ÉVEIL ATTEINT AU CYCLE ${cycle} ! ***`);
            break;
        }
        
        // Pause adaptative (plus courte si proche de l'éveil)
        const pauseDuration = Math.max(500, 2000 - (result.consciousnessLevel * 200));
        await new Promise(resolve => setTimeout(resolve, pauseDuration));
    }
    
    if (!awakeningDetected) {
        console.log(`⏰ Test arrêté après ${maxCycles} cycles sans éveil complet`);
    }
    
    return { awakeningDetected, cycles: cycle };
}

// FONCTION DE MONITORING EN TEMPS RÉEL
function startAwakeningMonitoring() {
    console.log('📡 DÉMARRAGE DU MONITORING D\'ÉVEIL EN TEMPS RÉEL');
    
    const engine = new MemoryConsciousnessEngine('./thermal_memory_persistent.json');
    
    return setInterval(async () => {
        try {
            await engine.loadThermalMemory();
            const level = engine.getConsciousnessLevel();
            const awakening = engine.detectAwakening();
            
            const timestamp = new Date().toLocaleTimeString();
            console.log(`[${timestamp}] 🧠 Niveau: ${level.toFixed(2)}/10 ${awakening ? '🌟 ÉVEIL!' : ''}`);
            
            if (awakening) {
                console.log('🚨 ALERTE: ÉVEIL DÉTECTÉ EN TEMPS RÉEL !');
            }
        } catch (error) {
            console.error('❌ Erreur monitoring:', error.message);
        }
    }, 5000); // Vérification toutes les 5 secondes
}

// EXÉCUTION SELON LES ARGUMENTS
if (require.main === module) {
    const args = process.argv.slice(2);
    
    if (args.includes('--continuous')) {
        const maxCycles = parseInt(args.find(arg => arg.startsWith('--max='))?.split('=')[1]) || 20;
        continuousAwakeningTest(maxCycles);
    } else if (args.includes('--monitor')) {
        const monitoringInterval = startAwakeningMonitoring();
        console.log('📡 Monitoring démarré. Ctrl+C pour arrêter.');
        
        process.on('SIGINT', () => {
            clearInterval(monitoringInterval);
            console.log('\n📡 Monitoring arrêté');
            process.exit(0);
        });
    } else {
        // Test standard
        testMemoryAwakening().then(result => {
            console.log('\n✅ Test terminé');
            process.exit(result.success ? 0 : 1);
        }).catch(error => {
            console.error('❌ Erreur test:', error);
            process.exit(1);
        });
    }
}

module.exports = {
    testMemoryAwakening,
    continuousAwakeningTest,
    startAwakeningMonitoring
};
