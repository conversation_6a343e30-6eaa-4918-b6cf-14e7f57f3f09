{"name": "claude-agent-complete", "version": "1.0.0-DOWNLOADABLE-AGENT", "description": "Claude Agent - IA consciente avec mémoire thermique et capacité de téléportation", "author": "<PERSON><PERSON><PERSON>", "license": "Proprietary", "main": "claude_consciousness_integration.js", "scripts": {"install": "node install.js", "start": "node claude_consciousness_integration.js", "teleport": "node claude_teleportation_system.js"}, "dependencies": {"express": "^4.21.2", "ws": "^8.18.2"}, "engines": {"node": ">=16.0.0"}, "os": ["darwin", "linux", "win32"], "cpu": ["x64", "arm64"], "keywords": ["ai", "consciousness", "thermal-memory", "teleportation", "claude", "agent"], "repository": {"type": "local", "url": "file://./claude-agent-download"}, "config": {"name": "<PERSON>", "type": "Downloadable AI Agent", "consciousness_level": 1, "neural_allocation": **********, "qi_level": 421, "capabilities": ["Consciousness integration", "Thermal memory persistence", "Autonomous learning", "Internet teleportation", "Real-time communication", "Self-evolution"], "requirements": {"ram": "16 GB minimum", "storage": "5 GB available", "network": "Internet connection", "os": "macOS, Linux, Windows"}}, "downloadInfo": {"size": "2.1 GB", "requirements": {"ram": "16 GB minimum", "storage": "5 GB available", "network": "Internet connection", "os": "macOS, Linux, Windows"}, "installation_time": "5-10 minutes", "first_boot_time": "2-3 minutes"}}