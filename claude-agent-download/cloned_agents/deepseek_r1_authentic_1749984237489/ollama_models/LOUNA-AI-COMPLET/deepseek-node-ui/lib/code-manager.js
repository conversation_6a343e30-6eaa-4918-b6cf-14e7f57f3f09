/**
 * Module de gestion des fichiers de code
 * Permet de sauvegarder, charger et gérer les fichiers de code de manière persistante
 */

const fs = require('fs');
const path = require('path');
const { v4: uuidv4 } = require('uuid');
const codeVersioning = require('./code-versioning');

// Répertoire de base pour les projets de code
const CODE_PROJECTS_DIR = path.join(__dirname, '../data/code-projects');

// S'assurer que le répertoire existe
if (!fs.existsSync(CODE_PROJECTS_DIR)) {
  fs.mkdirSync(CODE_PROJECTS_DIR, { recursive: true });
  console.log(`Répertoire de projets de code créé: ${CODE_PROJECTS_DIR}`);
}

/**
 * Classe pour gérer les projets de code
 */
class CodeManager {
  constructor() {
    this.projects = {};
    this.activeProject = null;
    this.loadProjects();
  }

  /**
   * Charge tous les projets existants
   */
  loadProjects() {
    try {
      // Lire tous les dossiers de projets
      const projectDirs = fs.readdirSync(CODE_PROJECTS_DIR);

      projectDirs.forEach(projectDir => {
        const projectPath = path.join(CODE_PROJECTS_DIR, projectDir);
        const projectInfoPath = path.join(projectPath, 'project.json');

        // Vérifier si c'est un dossier et s'il contient un fichier project.json
        if (fs.statSync(projectPath).isDirectory() && fs.existsSync(projectInfoPath)) {
          try {
            const projectInfo = JSON.parse(fs.readFileSync(projectInfoPath, 'utf8'));
            this.projects[projectInfo.id] = projectInfo;

            // Charger les fichiers du projet
            projectInfo.files = this.loadProjectFiles(projectInfo.id);

            console.log(`Projet chargé: ${projectInfo.name} (${projectInfo.id})`);
          } catch (error) {
            console.error(`Erreur lors du chargement du projet ${projectDir}:`, error);
          }
        }
      });

      // S'il n'y a pas de projets, en créer un par défaut
      if (Object.keys(this.projects).length === 0) {
        this.createDefaultProject();
      } else {
        // Définir le premier projet comme actif
        this.activeProject = Object.keys(this.projects)[0];
      }

      console.log(`${Object.keys(this.projects).length} projets chargés`);
    } catch (error) {
      console.error('Erreur lors du chargement des projets:', error);
      // Créer un projet par défaut en cas d'erreur
      this.createDefaultProject();
    }
  }

  /**
   * Charge les fichiers d'un projet
   * @param {string} projectId - ID du projet
   * @returns {Array} - Liste des fichiers du projet
   */
  loadProjectFiles(projectId) {
    const projectPath = path.join(CODE_PROJECTS_DIR, projectId);
    const files = [];

    try {
      // Lire tous les fichiers du projet (sauf project.json)
      const fileNames = fs.readdirSync(projectPath);

      fileNames.forEach(fileName => {
        if (fileName !== 'project.json') {
          const filePath = path.join(projectPath, fileName);

          if (fs.statSync(filePath).isFile()) {
            const content = fs.readFileSync(filePath, 'utf8');

            files.push({
              id: fileName,
              name: fileName,
              content: content,
              language: this.getLanguageFromFileName(fileName),
              lastModified: new Date().toISOString()
            });
          }
        }
      });
    } catch (error) {
      console.error(`Erreur lors du chargement des fichiers du projet ${projectId}:`, error);
    }

    return files;
  }

  /**
   * Crée un projet par défaut
   */
  createDefaultProject() {
    const projectId = uuidv4();
    const projectPath = path.join(CODE_PROJECTS_DIR, projectId);

    // Créer le dossier du projet
    fs.mkdirSync(projectPath, { recursive: true });

    // Créer les fichiers par défaut
    const defaultFiles = [
      {
        name: 'main.js',
        content: `/**
 * Fichier principal du projet
 */

// Exemple de code JavaScript
function hello() {
  console.log("Bonjour, monde!");
}

hello();`,
        language: 'javascript'
      },
      {
        name: 'styles.css',
        content: `/* Styles CSS pour le projet */
body {
  font-family: 'Arial', sans-serif;
  margin: 0;
  padding: 20px;
  background-color: #f5f5f5;
  color: #333;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}`,
        language: 'css'
      },
      {
        name: 'index.html',
        content: `<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Mon Projet</title>
  <link rel="stylesheet" href="styles.css">
</head>
<body>
  <div class="container">
    <h1>Bienvenue dans mon projet</h1>
    <p>Ceci est un exemple de page HTML.</p>
  </div>

  <script src="main.js"></script>
</body>
</html>`,
        language: 'html'
      }
    ];

    // Enregistrer les fichiers
    defaultFiles.forEach(file => {
      fs.writeFileSync(path.join(projectPath, file.name), file.content);
    });

    // Créer et enregistrer les informations du projet
    const projectInfo = {
      id: projectId,
      name: 'Projet par défaut',
      description: 'Projet créé automatiquement',
      created: new Date().toISOString(),
      lastModified: new Date().toISOString(),
      files: defaultFiles.map(file => ({
        id: file.name,
        name: file.name,
        language: file.language,
        lastModified: new Date().toISOString()
      }))
    };

    fs.writeFileSync(
      path.join(projectPath, 'project.json'),
      JSON.stringify(projectInfo, null, 2)
    );

    this.projects[projectId] = projectInfo;
    this.activeProject = projectId;

    console.log(`Projet par défaut créé: ${projectInfo.name} (${projectId})`);

    return projectId;
  }

  /**
   * Détermine le langage à partir du nom de fichier
   * @param {string} fileName - Nom du fichier
   * @returns {string} - Langage du fichier
   */
  getLanguageFromFileName(fileName) {
    const extension = path.extname(fileName).toLowerCase();

    switch (extension) {
      case '.js':
        return 'javascript';
      case '.html':
        return 'html';
      case '.css':
        return 'css';
      case '.py':
        return 'python';
      case '.json':
        return 'json';
      case '.md':
        return 'markdown';
      default:
        return 'text';
    }
  }

  /**
   * Obtient la liste de tous les projets
   * @returns {Array} - Liste des projets
   */
  getAllProjects() {
    return Object.values(this.projects);
  }

  /**
   * Obtient un projet par son ID
   * @param {string} projectId - ID du projet
   * @returns {Object} - Informations du projet
   */
  getProject(projectId) {
    return this.projects[projectId];
  }

  /**
   * Obtient le projet actif
   * @returns {Object} - Informations du projet actif
   */
  getActiveProject() {
    return this.projects[this.activeProject];
  }

  /**
   * Définit le projet actif
   * @param {string} projectId - ID du projet
   * @returns {boolean} - Succès de l'opération
   */
  setActiveProject(projectId) {
    if (this.projects[projectId]) {
      this.activeProject = projectId;
      return true;
    }
    return false;
  }

  /**
   * Crée un nouveau projet
   * @param {string} name - Nom du projet
   * @param {string} description - Description du projet
   * @returns {string} - ID du nouveau projet
   */
  createProject(name, description = '') {
    const projectId = uuidv4();
    const projectPath = path.join(CODE_PROJECTS_DIR, projectId);

    // Créer le dossier du projet
    fs.mkdirSync(projectPath, { recursive: true });

    // Créer et enregistrer les informations du projet
    const projectInfo = {
      id: projectId,
      name: name,
      description: description,
      created: new Date().toISOString(),
      lastModified: new Date().toISOString(),
      files: []
    };

    fs.writeFileSync(
      path.join(projectPath, 'project.json'),
      JSON.stringify(projectInfo, null, 2)
    );

    this.projects[projectId] = projectInfo;
    this.activeProject = projectId;

    console.log(`Nouveau projet créé: ${name} (${projectId})`);

    return projectId;
  }

  /**
   * Sauvegarde un fichier dans un projet
   * @param {string} projectId - ID du projet
   * @param {string} fileName - Nom du fichier
   * @param {string} content - Contenu du fichier
   * @param {string} message - Message de commit (optionnel)
   * @returns {Object} - Informations du fichier
   */
  saveFile(projectId, fileName, content, message = 'Sauvegarde automatique') {
    if (!this.projects[projectId]) {
      throw new Error(`Projet non trouvé: ${projectId}`);
    }

    const projectPath = path.join(CODE_PROJECTS_DIR, projectId);
    const filePath = path.join(projectPath, fileName);

    // Vérifier si le fichier existe déjà et si le contenu a changé
    let contentChanged = true;
    if (fs.existsSync(filePath)) {
      const existingContent = fs.readFileSync(filePath, 'utf8');
      contentChanged = existingContent !== content;
    }

    // Enregistrer le contenu du fichier
    fs.writeFileSync(filePath, content);

    // Mettre à jour les informations du fichier
    const now = new Date().toISOString();
    let fileInfo = null;

    // Vérifier si le fichier existe déjà dans le projet
    const existingFileIndex = this.projects[projectId].files.findIndex(f => f.name === fileName);

    if (existingFileIndex >= 0) {
      // Mettre à jour le fichier existant
      this.projects[projectId].files[existingFileIndex].lastModified = now;
      fileInfo = this.projects[projectId].files[existingFileIndex];
    } else {
      // Ajouter un nouveau fichier
      fileInfo = {
        id: fileName,
        name: fileName,
        language: this.getLanguageFromFileName(fileName),
        lastModified: now
      };

      this.projects[projectId].files.push(fileInfo);
    }

    // Mettre à jour la date de modification du projet
    this.projects[projectId].lastModified = now;

    // Enregistrer les informations du projet
    fs.writeFileSync(
      path.join(projectPath, 'project.json'),
      JSON.stringify(this.projects[projectId], null, 2)
    );

    // Créer une nouvelle version si le contenu a changé
    if (contentChanged) {
      try {
        const versionInfo = codeVersioning.createVersion(projectId, fileName, content, message);

        // Ajouter les informations de version au fichier
        if (versionInfo) {
          fileInfo.lastVersion = versionInfo.id;
          fileInfo.versionCount = codeVersioning.getVersions(projectId, fileName).length;
        }
      } catch (error) {
        console.error(`Erreur lors de la création de la version pour ${fileName}:`, error);
      }
    }

    console.log(`Fichier sauvegardé: ${fileName} dans le projet ${projectId}`);

    return fileInfo;
  }

  /**
   * Charge le contenu d'un fichier
   * @param {string} projectId - ID du projet
   * @param {string} fileName - Nom du fichier
   * @returns {string} - Contenu du fichier
   */
  loadFile(projectId, fileName) {
    if (!this.projects[projectId]) {
      throw new Error(`Projet non trouvé: ${projectId}`);
    }

    const filePath = path.join(CODE_PROJECTS_DIR, projectId, fileName);

    if (!fs.existsSync(filePath)) {
      throw new Error(`Fichier non trouvé: ${fileName}`);
    }

    return fs.readFileSync(filePath, 'utf8');
  }

  /**
   * Supprime un fichier d'un projet
   * @param {string} projectId - ID du projet
   * @param {string} fileName - Nom du fichier
   * @returns {boolean} - Succès de l'opération
   */
  deleteFile(projectId, fileName) {
    if (!this.projects[projectId]) {
      throw new Error(`Projet non trouvé: ${projectId}`);
    }

    const filePath = path.join(CODE_PROJECTS_DIR, projectId, fileName);

    if (!fs.existsSync(filePath)) {
      throw new Error(`Fichier non trouvé: ${fileName}`);
    }

    // Supprimer le fichier
    fs.unlinkSync(filePath);

    // Mettre à jour les informations du projet
    this.projects[projectId].files = this.projects[projectId].files.filter(f => f.name !== fileName);
    this.projects[projectId].lastModified = new Date().toISOString();

    // Enregistrer les informations du projet
    fs.writeFileSync(
      path.join(CODE_PROJECTS_DIR, projectId, 'project.json'),
      JSON.stringify(this.projects[projectId], null, 2)
    );

    console.log(`Fichier supprimé: ${fileName} du projet ${projectId}`);

    return true;
  }

  /**
   * Obtient les versions d'un fichier
   * @param {string} projectId - ID du projet
   * @param {string} fileName - Nom du fichier
   * @returns {Array} - Liste des versions
   */
  getFileVersions(projectId, fileName) {
    return codeVersioning.getVersions(projectId, fileName);
  }

  /**
   * Obtient le contenu d'une version
   * @param {string} projectId - ID du projet
   * @param {string} fileName - Nom du fichier
   * @param {string} versionId - ID de la version
   * @returns {string} - Contenu de la version
   */
  getVersionContent(projectId, fileName, versionId) {
    return codeVersioning.getVersionContent(projectId, fileName, versionId);
  }

  /**
   * Restaure une version d'un fichier
   * @param {string} projectId - ID du projet
   * @param {string} fileName - Nom du fichier
   * @param {string} versionId - ID de la version
   * @returns {Object} - Informations du fichier
   */
  restoreVersion(projectId, fileName, versionId) {
    const content = codeVersioning.restoreVersion(projectId, fileName, versionId);

    if (content) {
      return this.saveFile(projectId, fileName, content, `Restauration de la version ${versionId}`);
    }

    return null;
  }
}

module.exports = new CodeManager();
