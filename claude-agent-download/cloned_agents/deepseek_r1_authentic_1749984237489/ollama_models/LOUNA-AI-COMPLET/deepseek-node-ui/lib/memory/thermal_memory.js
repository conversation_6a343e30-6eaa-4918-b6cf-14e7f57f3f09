/**
 * Système de Mémoire Thermique pour l'agent DeepSeek r1
 *
 * Inspiré par le concept de mémoire thermique développé par Jean-Luc PASSAVE
 * Adapté pour une utilisation avec Ollama et Node.js
 *
 * Ce système gère plusieurs niveaux de mémoire avec des "températures" différentes
 * pour déterminer l'importance et la fraîcheur des informations.
 */

const fs = require('fs');
const path = require('path');
const { promisify } = require('util');

// Promisify des fonctions fs
const readFileAsync = promisify(fs.readFile);
const writeFileAsync = promisify(fs.writeFile);
const mkdirAsync = promisify(fs.mkdir);
const existsAsync = promisify(fs.exists);

// Chemin vers le dossier de données
const DATA_DIR = path.join(__dirname, '../../data/memory');

/**
 * Classe ThermalMemory - Gère la mémoire thermique de l'agent
 */
class ThermalMemory {
  /**
   * Initialise la mémoire thermique
   * @param {Object} config - Configuration de la mémoire thermique
   */
  constructor(config = {}) {
    // Configuration par défaut
    this.config = {
      memoryCycleInterval: config.memoryCycleInterval || 300, // secondes
      memoryDecayRate: config.memoryDecayRate || 0.95,

      // Capacités des différents niveaux de mémoire
      instantCapacity: config.instantCapacity || 20,
      shortTermCapacity: config.shortTermCapacity || 50,
      workingMemoryCapacity: config.workingMemoryCapacity || 100,
      mediumTermCapacity: config.mediumTermCapacity || 200,
      longTermCapacity: config.longTermCapacity || 1000,
      dreamMemoryCapacity: config.dreamMemoryCapacity || 50,

      // Seuils et taux de décroissance
      shortTermThreshold: config.shortTermThreshold || 0.3,
      shortTermDecay: config.shortTermDecay || 0.98,
      mediumTermThreshold: config.mediumTermThreshold || 0.6,
      mediumTermDecay: config.mediumTermDecay || 0.99,
      longTermDecay: config.longTermDecay || 0.999,

      // Paramètres de recherche
      searchDepth: config.searchDepth || 10,
      searchThreshold: config.searchThreshold || 0.5,

      // Facteurs d'importance
      importanceFactor: config.importanceFactor || 1.2,
      recencyFactor: config.recencyFactor || 1.1,
      accessFactor: config.accessFactor || 1.05
    };

    // Initialiser les niveaux de mémoire
    this.instantMemory = {}; // Mémoire instantanée (niveau 1)
    this.shortTerm = {};     // Mémoire à court terme (niveau 2)
    this.workingMemory = {}; // Mémoire de travail (niveau 3)
    this.mediumTerm = {};    // Mémoire à moyen terme (niveau 4)
    this.longTerm = {};      // Mémoire à long terme (niveau 5)
    this.dreamMemory = {};   // Mémoire des rêves (niveau 6)

    // Initialiser l'accélérateur Kyber
    this.kyber = {
      boostFactor: config.kyberBoostFactor || 1.5,
      temperature: config.kyberTemperature || 0.6,
      stability: config.kyberStability || 0.9,
      locked: config.kyberLocked || false,
      enabled: config.kyberEnabled !== undefined ? config.kyberEnabled : true
    };

    // Statistiques
    this.stats = {
      totalEntries: 0,
      instantEntries: 0,
      shortTermEntries: 0,
      workingMemoryEntries: 0,
      mediumTermEntries: 0,
      longTermEntries: 0,
      dreamMemoryEntries: 0,
      cyclesPerformed: 0,
      lastCycleTime: null,
      averageTemperature: 0.5
    };

    // Initialiser le dossier de données
    this.initDataDir();

    // Charger les données existantes
    this.loadMemory();

    // Démarrer le cycle de mémoire
    this.startMemoryCycle();

    console.log('Mémoire thermique initialisée');
  }

  /**
   * Initialise le dossier de données
   */
  async initDataDir() {
    try {
      if (!await existsAsync(DATA_DIR)) {
        await mkdirAsync(DATA_DIR, { recursive: true });
      }
    } catch (error) {
      console.error('Erreur lors de l\'initialisation du dossier de données:', error);
    }
  }

  /**
   * Charge les données de mémoire depuis les fichiers
   */
  async loadMemory() {
    try {
      // Charger la mémoire de travail
      const workingMemoryPath = path.join(DATA_DIR, 'working_memory.json');
      if (await existsAsync(workingMemoryPath)) {
        const data = await readFileAsync(workingMemoryPath, 'utf8');
        this.workingMemory = JSON.parse(data);
      }

      // Charger la mémoire à moyen terme
      const mediumTermPath = path.join(DATA_DIR, 'medium_term_memory.json');
      if (await existsAsync(mediumTermPath)) {
        const data = await readFileAsync(mediumTermPath, 'utf8');
        this.mediumTerm = JSON.parse(data);
      }

      // Charger la mémoire à long terme
      const longTermPath = path.join(DATA_DIR, 'long_term_memory.json');
      if (await existsAsync(longTermPath)) {
        const data = await readFileAsync(longTermPath, 'utf8');
        this.longTerm = JSON.parse(data);
      }

      // Charger la mémoire des rêves
      const dreamMemoryPath = path.join(DATA_DIR, 'dream_memory.json');
      if (await existsAsync(dreamMemoryPath)) {
        const data = await readFileAsync(dreamMemoryPath, 'utf8');
        this.dreamMemory = JSON.parse(data);
      }

      // Mettre à jour les statistiques
      this.updateStats();

      console.log('Mémoire chargée avec succès');
    } catch (error) {
      console.error('Erreur lors du chargement de la mémoire:', error);
    }
  }

  /**
   * Sauvegarde les données de mémoire dans les fichiers
   */
  async saveMemory() {
    try {
      // Sauvegarder la mémoire de travail
      await writeFileAsync(
        path.join(DATA_DIR, 'working_memory.json'),
        JSON.stringify(this.workingMemory, null, 2)
      );

      // Sauvegarder la mémoire à moyen terme
      await writeFileAsync(
        path.join(DATA_DIR, 'medium_term_memory.json'),
        JSON.stringify(this.mediumTerm, null, 2)
      );

      // Sauvegarder la mémoire à long terme
      await writeFileAsync(
        path.join(DATA_DIR, 'long_term_memory.json'),
        JSON.stringify(this.longTerm, null, 2)
      );

      // Sauvegarder la mémoire des rêves
      await writeFileAsync(
        path.join(DATA_DIR, 'dream_memory.json'),
        JSON.stringify(this.dreamMemory, null, 2)
      );

      console.log('Mémoire sauvegardée avec succès');
    } catch (error) {
      console.error('Erreur lors de la sauvegarde de la mémoire:', error);
    }
  }

  /**
   * Ajoute une information à la mémoire
   * @param {string} key - Clé pour identifier l'information
   * @param {Object} data - Données à stocker
   * @param {number} importance - Importance de l'information (0.0-1.0)
   * @param {string} category - Catégorie de l'information
   * @returns {string} - ID de l'entrée ajoutée
   */
  add(key, data, importance = 0.5, category = 'general') {
    // Générer un ID unique
    const id = `mem_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // Créer l'entrée
    const entry = {
      id,
      key,
      data,
      // Température initiale basée sur l'importance, avec ou sans accélération Kyber
      temperature: Math.min(0.9, importance * (this.kyber.enabled ? this.kyber.boostFactor : 1.0)),
      timestamp: Date.now(),
      lastAccessed: Date.now(),
      accessCount: 1,
      category,
      source: 'user_input'
    };

    // Déterminer le niveau de mémoire en fonction de la température
    if (entry.temperature >= 0.8) {
      // Mémoire instantanée pour les informations très importantes
      this.instantMemory[id] = entry;
      this.stats.instantEntries++;
    } else if (entry.temperature >= 0.6) {
      // Mémoire à court terme pour les informations importantes
      this.shortTerm[id] = entry;
      this.stats.shortTermEntries++;
    } else {
      // Mémoire de travail pour les informations standard
      this.workingMemory[id] = entry;
      this.stats.workingMemoryEntries++;
    }

    // Mettre à jour les statistiques
    this.stats.totalEntries++;

    // Sauvegarder la mémoire si c'est une information importante
    if (entry.temperature >= 0.7) {
      this.saveMemory();
    }

    return id;
  }

  /**
   * Récupère une information spécifique de la mémoire
   * @param {string} id - ID de l'entrée à récupérer
   * @returns {Object|null} - L'entrée récupérée ou null si non trouvée
   */
  get(id) {
    // Chercher dans tous les niveaux de mémoire
    let entry = this.instantMemory[id] ||
                this.shortTerm[id] ||
                this.workingMemory[id] ||
                this.mediumTerm[id] ||
                this.longTerm[id] ||
                this.dreamMemory[id];

    if (entry) {
      // Mettre à jour les statistiques d'accès
      entry.lastAccessed = Date.now();
      entry.accessCount++;

      // Augmenter légèrement la température à chaque accès
      entry.temperature = Math.min(1.0, entry.temperature * this.config.accessFactor);

      return entry;
    }

    return null;
  }

  /**
   * Recherche des informations pertinentes dans la mémoire
   * @param {string} query - Requête de recherche
   * @param {number} limit - Nombre maximum de résultats
   * @returns {Array} - Résultats de la recherche
   */
  retrieve(query, limit = 5) {
    const results = [];
    const searchTerms = query.toLowerCase().split(' ');

    // Fonction pour calculer la pertinence d'une entrée
    const calculateRelevance = (entry) => {
      const key = entry.key.toLowerCase();
      const data = JSON.stringify(entry.data).toLowerCase();

      // Calculer le score de correspondance
      let matchScore = 0;
      searchTerms.forEach(term => {
        if (term.length > 2) { // Ignorer les termes trop courts
          if (key.includes(term)) matchScore += 0.5;
          if (data.includes(term)) matchScore += 0.3;
        }
      });

      // Ajuster le score en fonction de la température et de la récence
      const recencyFactor = Math.max(0.1, Math.min(1.0, (Date.now() - entry.timestamp) / (1000 * 60 * 60 * 24 * 7))); // 7 jours
      const adjustedScore = matchScore * (entry.temperature * 0.7 + recencyFactor * 0.3);

      return adjustedScore;
    };

    // Collecter toutes les entrées de tous les niveaux de mémoire
    const allEntries = [
      ...Object.values(this.instantMemory),
      ...Object.values(this.shortTerm),
      ...Object.values(this.workingMemory),
      ...Object.values(this.mediumTerm),
      ...Object.values(this.longTerm),
      ...Object.values(this.dreamMemory)
    ];

    // Calculer la pertinence pour chaque entrée
    const scoredEntries = allEntries.map(entry => ({
      entry,
      relevance: calculateRelevance(entry)
    }));

    // Filtrer les entrées avec une pertinence suffisante
    const filteredEntries = scoredEntries.filter(item => item.relevance > this.config.searchThreshold);

    // Trier par pertinence et limiter le nombre de résultats
    const sortedEntries = filteredEntries
      .sort((a, b) => b.relevance - a.relevance)
      .slice(0, limit);

    // Mettre à jour les statistiques d'accès pour les entrées récupérées
    sortedEntries.forEach(item => {
      item.entry.lastAccessed = Date.now();
      item.entry.accessCount++;
      item.entry.temperature = Math.min(1.0, item.entry.temperature * this.config.accessFactor);
    });

    return sortedEntries.map(item => item.entry);
  }

  /**
   * Effectue un cycle de mémoire pour mettre à jour les températures et déplacer les entrées
   */
  async performMemoryCycle() {
    console.log('Exécution d\'un cycle de mémoire...');

    // Mettre à jour les statistiques
    this.stats.cyclesPerformed++;
    this.stats.lastCycleTime = Date.now();

    // Appliquer la décroissance de température à tous les niveaux de mémoire
    this.applyDecay(this.instantMemory, this.config.shortTermDecay);
    this.applyDecay(this.shortTerm, this.config.shortTermDecay);
    this.applyDecay(this.workingMemory, this.config.mediumTermDecay);
    this.applyDecay(this.mediumTerm, this.config.mediumTermDecay);
    this.applyDecay(this.longTerm, this.config.longTermDecay);
    this.applyDecay(this.dreamMemory, this.config.mediumTermDecay);

    // Déplacer les entrées entre les niveaux de mémoire
    this.moveEntries();

    // Générer un rêve si nécessaire (10% de chance à chaque cycle)
    if (Math.random() < 0.1) {
      this.generateDream();
    }

    // Mettre à jour les statistiques
    this.updateStats();

    // Sauvegarder la mémoire
    await this.saveMemory();

    console.log('Cycle de mémoire terminé');
  }

  /**
   * Applique la décroissance de température à un niveau de mémoire
   * @param {Object} memoryLevel - Niveau de mémoire à mettre à jour
   * @param {number} decayRate - Taux de décroissance
   */
  applyDecay(memoryLevel, decayRate) {
    Object.values(memoryLevel).forEach(entry => {
      // Calculer le temps écoulé depuis le dernier accès (en heures)
      const hoursSinceLastAccess = (Date.now() - entry.lastAccessed) / (1000 * 60 * 60);

      // Appliquer la décroissance en fonction du temps écoulé
      const decayFactor = Math.pow(decayRate, hoursSinceLastAccess / 24); // Normaliser sur une journée
      entry.temperature *= decayFactor;
    });
  }

  /**
   * Déplace les entrées entre les niveaux de mémoire en fonction de leur température
   */
  moveEntries() {
    // Déplacer de la mémoire instantanée vers la mémoire à court terme
    this.moveEntriesBetweenLevels(
      this.instantMemory,
      this.shortTerm,
      entry => entry.temperature < 0.7,
      this.config.shortTermCapacity
    );

    // Déplacer de la mémoire à court terme vers la mémoire de travail
    this.moveEntriesBetweenLevels(
      this.shortTerm,
      this.workingMemory,
      entry => entry.temperature < 0.5,
      this.config.workingMemoryCapacity
    );

    // Déplacer de la mémoire de travail vers la mémoire à moyen terme
    this.moveEntriesBetweenLevels(
      this.workingMemory,
      this.mediumTerm,
      entry => entry.temperature < 0.3,
      this.config.mediumTermCapacity
    );

    // Déplacer de la mémoire à moyen terme vers la mémoire à long terme (consolidation)
    this.moveEntriesBetweenLevels(
      this.mediumTerm,
      this.longTerm,
      entry => entry.temperature < 0.2 && entry.accessCount > 3,
      this.config.longTermCapacity
    );

    // Promotion: déplacer de la mémoire à moyen terme vers la mémoire de travail
    this.moveEntriesBetweenLevels(
      this.mediumTerm,
      this.workingMemory,
      entry => entry.temperature > 0.6,
      this.config.workingMemoryCapacity
    );

    // Promotion: déplacer de la mémoire à long terme vers la mémoire à moyen terme
    this.moveEntriesBetweenLevels(
      this.longTerm,
      this.mediumTerm,
      entry => entry.temperature > 0.4,
      this.config.mediumTermCapacity
    );
  }

  /**
   * Déplace les entrées d'un niveau de mémoire à un autre
   * @param {Object} sourceLevel - Niveau de mémoire source
   * @param {Object} targetLevel - Niveau de mémoire cible
   * @param {Function} condition - Fonction de condition pour le déplacement
   * @param {number} targetCapacity - Capacité maximale du niveau cible
   */
  moveEntriesBetweenLevels(sourceLevel, targetLevel, condition, targetCapacity) {
    // Vérifier si le niveau cible a atteint sa capacité
    if (Object.keys(targetLevel).length >= targetCapacity) {
      // Supprimer les entrées les moins importantes du niveau cible
      this.pruneMemoryLevel(targetLevel, targetCapacity * 0.9);
    }

    // Identifier les entrées à déplacer
    const entriesToMove = Object.entries(sourceLevel)
      .filter(([_, entry]) => condition(entry));

    // Déplacer les entrées
    entriesToMove.forEach(([id, entry]) => {
      targetLevel[id] = entry;
      delete sourceLevel[id];
    });
  }

  /**
   * Supprime les entrées les moins importantes d'un niveau de mémoire
   * @param {Object} memoryLevel - Niveau de mémoire à nettoyer
   * @param {number} targetSize - Taille cible après nettoyage
   */
  pruneMemoryLevel(memoryLevel, targetSize) {
    // Trier les entrées par température (importance)
    const sortedEntries = Object.entries(memoryLevel)
      .sort(([_, a], [__, b]) => a.temperature - b.temperature);

    // Supprimer les entrées les moins importantes
    const entriesToRemove = sortedEntries.slice(0, Object.keys(memoryLevel).length - targetSize);

    entriesToRemove.forEach(([id, _]) => {
      delete memoryLevel[id];
    });
  }

  /**
   * Génère un "rêve" basé sur les informations existantes
   */
  generateDream() {
    console.log('Génération d\'un rêve...');

    // Collecter des informations de différents niveaux de mémoire
    const allEntries = [
      ...Object.values(this.workingMemory),
      ...Object.values(this.mediumTerm),
      ...Object.values(this.longTerm)
    ];

    if (allEntries.length < 3) {
      console.log('Pas assez d\'informations pour générer un rêve');
      return;
    }

    // Sélectionner aléatoirement quelques entrées
    const selectedEntries = [];
    for (let i = 0; i < Math.min(5, allEntries.length); i++) {
      const randomIndex = Math.floor(Math.random() * allEntries.length);
      selectedEntries.push(allEntries[randomIndex]);
      allEntries.splice(randomIndex, 1);
    }

    // Créer un "rêve" en combinant les informations
    const dreamContent = {
      theme: 'Rêve généré automatiquement',
      elements: selectedEntries.map(entry => ({
        key: entry.key,
        data: entry.data,
        temperature: entry.temperature
      })),
      insights: 'Connexions entre différentes informations',
      timestamp: Date.now()
    };

    // Ajouter le rêve à la mémoire des rêves
    const dreamId = `dream_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    this.dreamMemory[dreamId] = {
      id: dreamId,
      key: dreamContent.theme,
      data: dreamContent,
      temperature: 0.6,
      timestamp: Date.now(),
      lastAccessed: Date.now(),
      accessCount: 1,
      category: 'dream',
      source: 'dream_generator'
    };

    this.stats.dreamMemoryEntries++;

    console.log('Rêve généré avec succès');
  }

  /**
   * Met à jour les statistiques de la mémoire
   */
  updateStats() {
    this.stats.instantEntries = Object.keys(this.instantMemory).length;
    this.stats.shortTermEntries = Object.keys(this.shortTerm).length;
    this.stats.workingMemoryEntries = Object.keys(this.workingMemory).length;
    this.stats.mediumTermEntries = Object.keys(this.mediumTerm).length;
    this.stats.longTermEntries = Object.keys(this.longTerm).length;
    this.stats.dreamMemoryEntries = Object.keys(this.dreamMemory).length;
    this.stats.totalEntries = this.stats.instantEntries +
                             this.stats.shortTermEntries +
                             this.stats.workingMemoryEntries +
                             this.stats.mediumTermEntries +
                             this.stats.longTermEntries +
                             this.stats.dreamMemoryEntries;

    // Calculer la température moyenne
    let totalTemperature = 0;
    let entryCount = 0;

    [this.instantMemory, this.shortTerm, this.workingMemory, this.mediumTerm, this.longTerm, this.dreamMemory]
      .forEach(memoryLevel => {
        Object.values(memoryLevel).forEach(entry => {
          totalTemperature += entry.temperature;
          entryCount++;
        });
      });

    this.stats.averageTemperature = entryCount > 0 ? totalTemperature / entryCount : 0;
  }

  /**
   * Démarre le cycle de mémoire périodique
   */
  startMemoryCycle() {
    // Exécuter un cycle immédiatement
    this.performMemoryCycle();

    // Planifier les cycles suivants
    setInterval(() => {
      this.performMemoryCycle();
    }, this.config.memoryCycleInterval * 1000);
  }

  /**
   * Retourne les statistiques de la mémoire
   * @returns {Object} - Statistiques de la mémoire
   */
  getStats() {
    this.updateStats();
    return this.stats;
  }

  /**
   * Active l'accélérateur Kyber
   * @returns {boolean} - True si l'activation a réussi
   */
  enableKyberAccelerator() {
    if (this.kyber.locked) {
      console.log('Accélérateur Kyber verrouillé, impossible de modifier son état');
      return false;
    }

    this.kyber.enabled = true;
    console.log('Accélérateur Kyber activé');
    return true;
  }

  /**
   * Désactive l'accélérateur Kyber
   * @returns {boolean} - True si la désactivation a réussi
   */
  disableKyberAccelerator() {
    if (this.kyber.locked) {
      console.log('Accélérateur Kyber verrouillé, impossible de modifier son état');
      return false;
    }

    this.kyber.enabled = false;
    console.log('Accélérateur Kyber désactivé');
    return true;
  }

  /**
   * Configure l'accélérateur Kyber
   * @param {Object} config - Configuration de l'accélérateur
   * @returns {boolean} - True si la configuration a réussi
   */
  configureKyberAccelerator(config) {
    if (this.kyber.locked) {
      console.log('Accélérateur Kyber verrouillé, impossible de modifier sa configuration');
      return false;
    }

    if (config.boostFactor !== undefined) {
      this.kyber.boostFactor = Math.max(1.0, Math.min(2.0, config.boostFactor));
    }

    if (config.temperature !== undefined) {
      this.kyber.temperature = Math.max(0.1, Math.min(1.0, config.temperature));
    }

    if (config.stability !== undefined) {
      this.kyber.stability = Math.max(0.1, Math.min(1.0, config.stability));
    }

    if (config.enabled !== undefined) {
      this.kyber.enabled = config.enabled;
    }

    console.log('Configuration de l\'accélérateur Kyber mise à jour');
    return true;
  }

  /**
   * Verrouille ou déverrouille l'accélérateur Kyber
   * @param {boolean} locked - État de verrouillage
   * @returns {boolean} - True si le changement a réussi
   */
  setKyberLock(locked) {
    this.kyber.locked = locked;
    console.log(`Accélérateur Kyber ${locked ? 'verrouillé' : 'déverrouillé'}`);
    return true;
  }

  /**
   * Obtient l'état actuel de l'accélérateur Kyber
   * @returns {Object} - État de l'accélérateur
   */
  getKyberState() {
    return {
      ...this.kyber,
      effectiveBoost: this.kyber.enabled ? this.kyber.boostFactor : 1.0
    };
  }
}

// Instance unique de la mémoire thermique
let thermalMemoryInstance = null;

/**
 * Obtient l'instance unique de la mémoire thermique
 * @param {Object} config - Configuration de la mémoire thermique
 * @returns {ThermalMemory} - Instance de la mémoire thermique
 */
function getThermalMemory(config = {}) {
  if (!thermalMemoryInstance) {
    thermalMemoryInstance = new ThermalMemory(config);
  }
  return thermalMemoryInstance;
}

module.exports = {
  ThermalMemory,
  getThermalMemory
};
