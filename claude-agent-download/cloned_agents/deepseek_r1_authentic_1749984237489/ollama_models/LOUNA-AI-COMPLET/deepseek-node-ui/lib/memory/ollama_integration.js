/**
 * Intégration entre Ollama et la mémoire thermique
 *
 * Ce module permet d'enrichir les prompts envoyés à Ollama avec des informations
 * pertinentes de la mémoire thermique et de stocker les échanges dans la mémoire.
 */

const axios = require('axios');
const { getThermalMemory } = require('./thermal_memory');

/**
 * Classe OllamaIntegration - Gère l'intégration entre Ollama et la mémoire thermique
 */
class OllamaIntegration {
  /**
   * Initialise l'intégration Ollama-Mémoire thermique
   * @param {Object} config - Configuration de l'intégration
   */
  constructor(config = {}) {
    this.config = {
      ollamaApiUrl: config.ollamaApiUrl || 'http://localhost:11434/api',
      useMemory: config.useMemory !== undefined ? config.useMemory : true,
      maxContextItems: config.maxContextItems || 5,
      memoryImportance: config.memoryImportance || 0.7
    };

    // Initialiser la mémoire thermique
    if (this.config.useMemory) {
      this.thermalMemory = getThermalMemory();
      console.log('Mémoire thermique connectée à l\'intégration Ollama');
    } else {
      this.thermalMemory = null;
      console.log('Intégration Ollama sans mémoire thermique');
    }

    // Statistiques
    this.stats = {
      requests: 0,
      successfulRequests: 0,
      memoryEnrichedRequests: 0,
      averageResponseTime: 0,
      totalResponseTime: 0
    };
  }

  /**
   * Vérifie si Ollama est disponible
   * @returns {Promise<boolean>} - True si Ollama est disponible
   */
  async isOllamaAvailable() {
    try {
      await axios.get(`${this.config.ollamaApiUrl}/version`);
      return true;
    } catch (error) {
      console.error('Erreur lors de la vérification de la disponibilité d\'Ollama:', error.message);
      return false;
    }
  }

  /**
   * Appelle l'API Ollama avec un prompt enrichi
   * @param {string} prompt - Prompt à envoyer à Ollama
   * @param {Array} history - Historique de la conversation
   * @param {string} modelName - Nom du modèle à utiliser
   * @param {Object} options - Options supplémentaires
   * @returns {Promise<Object>} - Réponse d'Ollama
   */
  async callOllamaApi(prompt, history = [], modelName = 'deepseek-r1:7b', options = {}) {
    try {
      const startTime = Date.now();
      this.stats.requests++;

      // Vérifier si Ollama est disponible
      if (!await this.isOllamaAvailable()) {
        return { error: 'Ollama n\'est pas disponible' };
      }

      // Enrichir le prompt avec la mémoire thermique si demandé
      let enhancedPrompt = prompt;
      let memoryData = [];

      if (this.config.useMemory && this.thermalMemory) {
        // Récupérer les informations pertinentes de la mémoire
        memoryData = this.thermalMemory.retrieve(prompt, this.config.maxContextItems);

        if (memoryData && memoryData.length > 0) {
          // Construire un contexte à partir des informations de la mémoire
          let memoryContext = "\n\nInformations pertinentes de ma mémoire:\n";

          memoryData.forEach((item, index) => {
            if (item.data) {
              if (typeof item.data === 'object') {
                if (item.data.question && item.data.answer) {
                  memoryContext += `${index + 1}. Question: ${item.data.question}\nRéponse: ${item.data.answer}\n\n`;
                } else if (item.data.content) {
                  memoryContext += `${index + 1}. ${item.data.content}\n\n`;
                } else {
                  memoryContext += `${index + 1}. ${JSON.stringify(item.data)}\n\n`;
                }
              } else {
                memoryContext += `${index + 1}. ${item.data}\n\n`;
              }
            }
          });

          // Ajouter le contexte au prompt
          enhancedPrompt = `${memoryContext}\n\nQuestion actuelle: ${prompt}\n\nRéponds de manière précise et utile en te basant sur les informations disponibles.`;
          this.stats.memoryEnrichedRequests++;
        }
      }

      // Préparer les données pour l'API Ollama
      const requestData = {
        model: modelName,
        messages: [
          ...history,
          { role: 'user', content: enhancedPrompt }
        ],
        options: {
          temperature: options.temperature || 0.7,
          num_predict: options.maxTokens || 1000
        }
      };

      // Appeler l'API Ollama
      const response = await axios.post(`${this.config.ollamaApiUrl}/chat`, requestData);

      // Mettre à jour les statistiques
      this.stats.successfulRequests++;
      const responseTime = Date.now() - startTime;
      this.stats.totalResponseTime += responseTime;
      this.stats.averageResponseTime = this.stats.totalResponseTime / this.stats.successfulRequests;

      // Stocker l'échange dans la mémoire si demandé
      if (this.config.useMemory && this.thermalMemory && response.data && response.data.message) {
        try {
          // Créer une entrée dans la mémoire
          const entry = {
            question: prompt,
            answer: response.data.message.content,
            timestamp: new Date().toISOString(),
            model: modelName
          };

          // Ajouter l'entrée dans la mémoire
          this.thermalMemory.add(prompt, entry, this.config.memoryImportance, 'dialogue');
        } catch (memError) {
          console.error('Erreur lors de l\'ajout à la mémoire:', memError);
        }
      }

      return response.data;
    } catch (error) {
      console.error('Erreur lors de l\'appel à l\'API Ollama:', error.message);

      if (error.response) {
        return {
          error: `Erreur de communication avec Ollama: ${error.response.status} ${error.response.statusText}`,
          details: error.response.data
        };
      } else {
        return {
          error: 'Erreur de communication avec Ollama',
          details: error.message
        };
      }
    }
  }

  /**
   * Obtient une réponse simple à un prompt
   * @param {string} prompt - Prompt à envoyer à Ollama
   * @param {string} modelName - Nom du modèle à utiliser
   * @returns {Promise<string>} - Réponse d'Ollama
   */
  async getResponse(prompt, modelName = 'deepseek-r1:7b') {
    const response = await this.callOllamaApi(prompt, [], modelName);

    if (response.error) {
      return `Erreur: ${response.error}`;
    }

    return response.message ? response.message.content : 'Pas de réponse';
  }

  /**
   * Génère un état du cerveau pour la visualisation
   * @returns {Object} - État du cerveau
   */
  getBrainState() {
    // Obtenir les statistiques de la mémoire thermique
    const memoryStats = this.thermalMemory ? this.thermalMemory.getStats() : null;

    // Obtenir les informations CPU
    const cpuInfo = this.getCpuInfo();

    // Calculer les activités basées sur les statistiques de mémoire et CPU
    const frontalActivity = Math.min(98, Math.round(
      (memoryStats ? memoryStats.instantEntries * 5 : 0) +
      (cpuInfo.usage * 0.5) +
      50
    ));

    const parietalActivity = Math.min(98, Math.round(
      (memoryStats ? memoryStats.shortTermEntries * 3 : 0) +
      (cpuInfo.usage * 0.3) +
      45
    ));

    const temporalActivity = Math.min(98, Math.round(
      (memoryStats ? memoryStats.workingMemoryEntries * 2 : 0) +
      (cpuInfo.usage * 0.4) +
      55
    ));

    const occipitalActivity = Math.min(98, Math.round(
      (memoryStats ? memoryStats.mediumTermEntries * 1 : 0) +
      (cpuInfo.usage * 0.6) +
      40
    ));

    const limbicActivity = Math.min(98, Math.round(
      (memoryStats ? memoryStats.longTermEntries * 0.5 : 0) +
      (cpuInfo.usage * 0.2) +
      60
    ));

    const creativeActivity = Math.min(98, Math.round(
      (memoryStats ? memoryStats.dreamMemoryEntries * 4 : 0) +
      (cpuInfo.temperature * 5) +
      65
    ));

    // Calculer l'activité globale comme moyenne pondérée
    const globalActivity = Math.round(
      (frontalActivity + parietalActivity + temporalActivity +
       occipitalActivity + limbicActivity + creativeActivity) / 6
    );

    // Calculer les températures basées sur les statistiques CPU
    const baseTemp = cpuInfo.temperature || 36.5;

    // Calculer le score d'évolution basé sur les statistiques de mémoire
    const evolutionScore = memoryStats ?
      Math.round((60 +
        (memoryStats.totalEntries / 10) +
        (memoryStats.averageTemperature * 20) +
        (memoryStats.cyclesPerformed * 0.5)
      ) * 10) / 10 : 70;

    return {
      zones: {
        frontal: {
          activity: frontalActivity,
          temperature: Math.round((baseTemp + 0.2) * 10) / 10,
          entries: memoryStats ? memoryStats.instantEntries : 0
        },
        parietal: {
          activity: parietalActivity,
          temperature: Math.round((baseTemp - 0.1) * 10) / 10,
          entries: memoryStats ? memoryStats.shortTermEntries : 0
        },
        temporal: {
          activity: temporalActivity,
          temperature: Math.round((baseTemp + 0.3) * 10) / 10,
          entries: memoryStats ? memoryStats.workingMemoryEntries : 0
        },
        occipital: {
          activity: occipitalActivity,
          temperature: Math.round((baseTemp - 0.2) * 10) / 10,
          entries: memoryStats ? memoryStats.mediumTermEntries : 0
        },
        limbic: {
          activity: limbicActivity,
          temperature: Math.round((baseTemp + 0.1) * 10) / 10,
          entries: memoryStats ? memoryStats.longTermEntries : 0
        },
        creative: {
          activity: creativeActivity,
          temperature: Math.round((baseTemp + 0.4) * 10) / 10,
          entries: memoryStats ? memoryStats.dreamMemoryEntries : 0
        }
      },
      globalActivity,
      globalTemperature: Math.round(baseTemp * 10) / 10,
      evolutionScore,
      memoryStats,
      cpuInfo,
      kyber: this.thermalMemory ? this.thermalMemory.getKyberState() : null
    };
  }

  /**
   * Obtient les informations CPU
   * @returns {Object} - Informations CPU
   */
  getCpuInfo() {
    try {
      // Simuler les informations CPU (dans une vraie implémentation, on utiliserait os-utils ou systeminformation)
      const usage = Math.min(100, Math.round(50 + Math.random() * 30));
      const temperature = Math.round((36.5 + (usage / 100) * 3) * 10) / 10;
      const cores = 8;
      const memory = {
        total: 16384, // MB
        free: Math.round(4096 + Math.random() * 2048), // MB
        used: Math.round(8192 + Math.random() * 2048) // MB
      };

      return {
        usage,
        temperature,
        cores,
        memory
      };
    } catch (error) {
      console.error('Error getting CPU info:', error);
      return {
        usage: 50,
        temperature: 36.5,
        cores: 8,
        memory: {
          total: 16384,
          free: 8192,
          used: 8192
        }
      };
    }
  }

  /**
   * Retourne les statistiques de l'intégration
   * @returns {Object} - Statistiques de l'intégration
   */
  getStats() {
    return {
      ...this.stats,
      memoryStats: this.thermalMemory ? this.thermalMemory.getStats() : null
    };
  }
}

// Instance unique de l'intégration Ollama
let ollamaIntegrationInstance = null;

/**
 * Obtient l'instance unique de l'intégration Ollama
 * @param {Object} config - Configuration de l'intégration
 * @returns {OllamaIntegration} - Instance de l'intégration Ollama
 */
function getOllamaIntegration(config = {}) {
  if (!ollamaIntegrationInstance) {
    ollamaIntegrationInstance = new OllamaIntegration(config);
  }
  return ollamaIntegrationInstance;
}

module.exports = {
  OllamaIntegration,
  getOllamaIntegration
};
