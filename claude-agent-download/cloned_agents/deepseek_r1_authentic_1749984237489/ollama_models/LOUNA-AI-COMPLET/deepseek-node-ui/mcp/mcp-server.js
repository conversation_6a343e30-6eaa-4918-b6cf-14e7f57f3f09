/**
 * Serveur MCP (Model Context Protocol) pour DeepSeek
 *
 * Ce module implémente le protocole MCP pour permettre à l'assistant d'accéder à Internet
 * et d'effectuer des opérations sur le bureau de l'utilisateur.
 */

const express = require('express');
const http = require('http');
const fs = require('fs');
const path = require('path');
const os = require('os');
const axios = require('axios');
const { exec } = require('child_process');
const puppeteer = require('puppeteer');

class MCPServer {
  constructor(options = {}) {
    this.options = {
      port: options.port || 3001,
      allowInternet: true, // Toujours activé
      allowDesktop: true,  // Toujours activé
      allowSystemCommands: true, // Toujours activé
      debug: true // Mode debug activé pour faciliter le dépannage
    };

    this.app = express();
    this.server = http.createServer(this.app);
    this.desktopPath = path.join(os.homedir(), 'Desktop');
    this.documentsPath = path.join(os.homedir(), 'Documents');
    this.workingBrowser = null;

    this.setupRoutes();

    console.log('Serveur MCP initialisé avec les options:', this.options);
  }

  setupRoutes() {
    this.app.use(express.json({ limit: '50mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '50mb' }));

    // Route pour la vérification du statut
    this.app.get('/mcp/status', (req, res) => {
      res.json({
        status: 'ok',
        version: '1.0',
        capabilities: {
          internet: this.options.allowInternet,
          desktop: this.options.allowDesktop,
          systemCommands: this.options.allowSystemCommands
        }
      });
    });

    // Configurer toutes les routes, indépendamment des options
    // pour s'assurer que toutes les fonctionnalités sont disponibles
    this.setupInternetRoutes();
    this.setupDesktopRoutes();
    this.setupSystemCommandsRoutes();

    // Route pour vérifier l'accès au bureau
    this.app.get('/mcp/desktop/check', (req, res) => {
      try {
        const desktopExists = fs.existsSync(this.desktopPath);
        const desktopAccessible = desktopExists && fs.accessSync(this.desktopPath, fs.constants.R_OK | fs.constants.W_OK);

        res.json({
          success: true,
          desktopPath: this.desktopPath,
          exists: desktopExists,
          accessible: !!desktopAccessible
        });
      } catch (error) {
        res.json({
          success: false,
          error: error.message,
          desktopPath: this.desktopPath
        });
      }
    });
  }

  setupInternetRoutes() {
    // Route pour extraire des données d'une URL
    this.app.post('/mcp/internet/fetch', async (req, res) => {
      try {
        const { url, headers = {} } = req.body;

        if (!url) {
          return res.status(400).json({ error: 'URL requise' });
        }

        this.log(`Récupération des données depuis ${url}`);

        const response = await axios.get(url, { headers });

        res.json({
          success: true,
          data: response.data,
          headers: response.headers,
          status: response.status
        });
      } catch (error) {
        console.error('Erreur lors de la récupération des données:', error);
        res.status(500).json({
          success: false,
          error: error.message,
          stack: this.options.debug ? error.stack : undefined
        });
      }
    });

    // Route pour effectuer une recherche web
    this.app.post('/mcp/internet/search', async (req, res) => {
      try {
        const { query, limit = 5 } = req.body;

        if (!query) {
          return res.status(400).json({ error: 'Requête de recherche requise' });
        }

        this.log(`Recherche web pour: ${query}`);

        // Utilisation de l'API DuckDuckGo pour la recherche
        // Note: Dans une implémentation réelle, vous devriez utiliser une API de recherche comme Google, Bing, etc.
        const searchUrl = `https://api.duckduckgo.com/?q=${encodeURIComponent(query)}&format=json`;
        const response = await axios.get(searchUrl);

        res.json({
          success: true,
          results: response.data,
          query
        });
      } catch (error) {
        console.error('Erreur lors de la recherche web:', error);
        res.status(500).json({
          success: false,
          error: error.message,
          stack: this.options.debug ? error.stack : undefined
        });
      }
    });

    // Route pour scraper une page web avec Puppeteer
    this.app.post('/mcp/internet/scrape', async (req, res) => {
      try {
        const { url, selector, actions = [] } = req.body;

        if (!url) {
          return res.status(400).json({ error: 'URL requise' });
        }

        this.log(`Scraping de ${url}`);

        // Lancer le navigateur si ce n'est pas déjà fait
        if (!this.workingBrowser) {
          this.workingBrowser = await puppeteer.launch({
            headless: 'new',
            args: ['--no-sandbox', '--disable-setuid-sandbox']
          });
        }

        const page = await this.workingBrowser.newPage();
        await page.goto(url, { waitUntil: 'networkidle2' });

        // Exécuter les actions séquentiellement
        for (const action of actions) {
          if (action.type === 'click' && action.selector) {
            await page.click(action.selector);
            await page.waitForTimeout(1000);
          } else if (action.type === 'type' && action.selector && action.text) {
            await page.type(action.selector, action.text);
            await page.waitForTimeout(500);
          } else if (action.type === 'wait' && action.milliseconds) {
            await page.waitForTimeout(action.milliseconds);
          }
        }

        let result;

        if (selector) {
          // Extraire le contenu spécifique
          await page.waitForSelector(selector, { timeout: 5000 }).catch(() => {});
          result = await page.evaluate((sel) => {
            const element = document.querySelector(sel);
            return element ? element.innerText : null;
          }, selector);
        } else {
          // Extraire tout le contenu
          result = await page.content();
        }

        // Prendre une capture d'écran
        const screenshot = await page.screenshot({ encoding: 'base64' });

        await page.close();

        res.json({
          success: true,
          content: result,
          screenshot: `data:image/png;base64,${screenshot}`,
          url
        });
      } catch (error) {
        console.error('Erreur lors du scraping:', error);
        res.status(500).json({
          success: false,
          error: error.message,
          stack: this.options.debug ? error.stack : undefined
        });
      }
    });
  }

  setupDesktopRoutes() {
    // Route pour lister les fichiers sur le bureau
    this.app.get('/mcp/desktop/files', (req, res) => {
      try {
        const files = fs.readdirSync(this.desktopPath);

        const fileDetails = files.map(file => {
          const fullPath = path.join(this.desktopPath, file);
          const stats = fs.statSync(fullPath);

          return {
            name: file,
            path: fullPath,
            isDirectory: stats.isDirectory(),
            size: stats.size,
            created: stats.birthtime,
            modified: stats.mtime
          };
        });

        res.json({
          success: true,
          files: fileDetails,
          path: this.desktopPath
        });
      } catch (error) {
        console.error('Erreur lors de la lecture des fichiers du bureau:', error);
        res.status(500).json({
          success: false,
          error: error.message,
          stack: this.options.debug ? error.stack : undefined
        });
      }
    });

    // Route pour créer un dossier sur le bureau
    this.app.post('/mcp/desktop/createFolder', (req, res) => {
      try {
        const { folderName } = req.body;

        if (!folderName) {
          return res.status(400).json({ error: 'Nom de dossier requis' });
        }

        const folderPath = path.join(this.desktopPath, folderName);

        if (!fs.existsSync(folderPath)) {
          fs.mkdirSync(folderPath, { recursive: true });
          res.json({
            success: true,
            message: `Dossier "${folderName}" créé sur le bureau`,
            path: folderPath
          });
        } else {
          res.json({
            success: true,
            message: `Le dossier "${folderName}" existe déjà sur le bureau`,
            path: folderPath
          });
        }
      } catch (error) {
        console.error('Erreur lors de la création du dossier:', error);
        res.status(500).json({
          success: false,
          error: error.message,
          stack: this.options.debug ? error.stack : undefined
        });
      }
    });

    // Route pour créer un fichier sur le bureau
    this.app.post('/mcp/desktop/createFile', (req, res) => {
      try {
        const { fileName, content, encoding = 'utf8' } = req.body;

        if (!fileName) {
          return res.status(400).json({ error: 'Nom de fichier requis' });
        }

        const filePath = path.join(this.desktopPath, fileName);

        fs.writeFileSync(filePath, content || '', { encoding });

        res.json({
          success: true,
          message: `Fichier "${fileName}" créé sur le bureau`,
          path: filePath
        });
      } catch (error) {
        console.error('Erreur lors de la création du fichier:', error);
        res.status(500).json({
          success: false,
          error: error.message,
          stack: this.options.debug ? error.stack : undefined
        });
      }
    });

    // Route pour lire un fichier
    this.app.post('/mcp/desktop/readFile', (req, res) => {
      try {
        const { filePath, encoding = 'utf8' } = req.body;

        if (!filePath) {
          return res.status(400).json({ error: 'Chemin de fichier requis' });
        }

        const fullPath = path.isAbsolute(filePath) ? filePath : path.join(this.desktopPath, filePath);

        if (!fs.existsSync(fullPath)) {
          return res.status(404).json({ error: 'Fichier non trouvé' });
        }

        const stats = fs.statSync(fullPath);

        if (stats.isDirectory()) {
          return res.status(400).json({ error: 'Le chemin spécifié est un dossier, pas un fichier' });
        }

        const content = fs.readFileSync(fullPath, { encoding });

        res.json({
          success: true,
          content,
          size: stats.size,
          modified: stats.mtime,
          path: fullPath
        });
      } catch (error) {
        console.error('Erreur lors de la lecture du fichier:', error);
        res.status(500).json({
          success: false,
          error: error.message,
          stack: this.options.debug ? error.stack : undefined
        });
      }
    });
  }

  setupSystemCommandsRoutes() {
    // Route pour exécuter une commande système
    this.app.post('/mcp/system/execute', (req, res) => {
      try {
        const { command, timeout = 30000 } = req.body;

        if (!command) {
          return res.status(400).json({ error: 'Commande requise' });
        }

        // Liste de commandes dangereuses à bloquer
        const dangerousCommands = ['rm -rf', 'rmdir /s', 'format', 'sudo', 'shutdown', 'reboot'];

        if (dangerousCommands.some(cmd => command.toLowerCase().includes(cmd.toLowerCase()))) {
          return res.status(403).json({
            success: false,
            error: 'Cette commande est potentiellement dangereuse et a été bloquée'
          });
        }

        this.log(`Exécution de la commande: ${command}`);

        exec(command, { timeout }, (error, stdout, stderr) => {
          if (error) {
            return res.status(500).json({
              success: false,
              error: error.message,
              stderr
            });
          }

          res.json({
            success: true,
            stdout,
            stderr,
            command
          });
        });
      } catch (error) {
        console.error('Erreur lors de l\'exécution de la commande:', error);
        res.status(500).json({
          success: false,
          error: error.message,
          stack: this.options.debug ? error.stack : undefined
        });
      }
    });
  }

  start() {
    return new Promise((resolve, reject) => {
      this.server.listen(this.options.port, () => {
        console.log(`Serveur MCP démarré sur le port ${this.options.port}`);
        resolve(this.server);
      }).on('error', (err) => {
        console.error('Erreur lors du démarrage du serveur MCP:', err);
        reject(err);
      });
    });
  }

  stop() {
    return new Promise(async (resolve) => {
      // Fermer le navigateur Puppeteer s'il est ouvert
      if (this.workingBrowser) {
        await this.workingBrowser.close();
        this.workingBrowser = null;
      }

      // Fermer le serveur HTTP
      if (this.server && this.server.listening) {
        this.server.close(() => {
          console.log('Serveur MCP arrêté');
          resolve();
        });
      } else {
        resolve();
      }
    });
  }

  log(message) {
    if (this.options.debug) {
      console.log(`[MCP] ${message}`);
    }
  }
}

module.exports = MCPServer;
