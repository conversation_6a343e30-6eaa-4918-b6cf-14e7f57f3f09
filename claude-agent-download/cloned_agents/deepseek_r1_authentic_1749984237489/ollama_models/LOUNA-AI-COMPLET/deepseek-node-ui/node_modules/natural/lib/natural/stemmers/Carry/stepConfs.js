/*
Copyright (c) 2020, <PERSON>

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in
all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
THE SOFTWARE.
*/

'use strict'

const steps = [
  // === Step 1
  [
    // Transformations with 1 as minimal radix
    {
      issaient: '',
      ellement: 'el',
      issement: '',
      alement: 'al',
      eraient: '',
      iraient: '',
      eassent: '',
      ussent: '',
      amment: '',
      emment: '',
      issant: '',
      issent: '',
      assent: '',
      eaient: '',
      issait: '',
      èrent: '',
      erent: '',
      irent: '',
      erait: '',
      irait: '',
      iront: '',
      eront: '',
      ement: '',
      aient: '',
      îrent: '',
      eont: '',
      eant: '',
      eait: '',
      ient: '',
      ent: '',
      ont: '',
      ant: '',
      eât: '',
      ait: '',
      at: '',
      ât: '',
      it: '',
      ît: '',
      t: '',
      uction: '',
      ition: '',
      tion: '',
      eur: '',
      ier: '',
      er: '',
      ir: '',
      r: '',
      eassiez: '',
      issiez: '',
      assiez: '',
      ussiez: '',
      issez: '',
      assez: '',
      eriez: '',
      iriez: '',
      erez: '',
      irez: '',
      iez: '',
      ez: '',
      erai: '',
      irai: '',
      eai: '',
      ai: '',
      i: '',
      ira: '',
      era: '',
      ea: '',
      a: '',
      f: 'v',
      yeux: 'oeil',
      eux: '',
      aux: 'al',
      x: '',
      issante: '',
      eresse: '',
      eante: '',
      easse: '',
      eure: '',
      esse: '',
      asse: '',
      ance: '',
      ence: '',
      aise: '',
      euse: '',
      oise: 'o',
      isse: '',
      ante: '',
      ouse: 'ou',
      ière: '',
      ete: '',
      ète: '',
      iere: '',
      aire: '',
      erie: '',
      étude: '',
      etude: '',
      itude: '',
      ade: '',
      isme: '',
      age: '',
      trice: '',
      cque: 'c',
      que: 'c',
      eille: 'eil',
      elle: '',
      able: '',
      iste: '',
      ulle: 'ul',
      gue: 'g',
      ette: '',
      nne: 'n',
      itée: '',
      ité: '',
      té: '',
      ée: '',
      é: '',
      usse: '',
      // aise: '',
      ate: '',
      ite: '',
      ee: '',
      e: '',
      issements: '',
      issantes: '',
      eassions: '',
      eresses: '',
      issions: '',
      assions: '',
      issants: '',
      ussions: '',
      ements: '',
      eantes: '',
      issons: '',
      assons: '',
      easses: '',
      études: '',
      etudes: '',
      itudes: '',
      issais: '',
      trices: '',
      eilles: 'eil',
      irions: '',
      erions: '',
      usses: '',
      tions: '',
      ances: '',
      entes: '',
      eants: '',
      ables: '',
      irons: '',
      irais: '',
      ences: '',
      ients: '',
      ieres: '',
      eures: '',
      aires: '',
      erons: '',
      esses: '',
      euses: '',
      ulles: 'ul',
      cques: 'c',
      elles: '',
      // ables: '',
      istes: '',
      aises: '',
      asses: '',
      isses: '',
      oises: 'o',
      // tions: '',
      ouses: 'ou',
      ières: '',
      eries: '',
      antes: '',
      ismes: '',
      erais: '',
      eâtes: '',
      eâmes: '',
      itées: '',
      ettes: '',
      ages: '',
      eurs: '',
      ents: '',
      ètes: '',
      etes: '',
      ions: '',
      ités: '',
      ites: '',
      ates: '',
      âtes: '',
      îtes: '',
      // eurs: '',
      iers: '',
      iras: '',
      eras: '',
      ants: '',
      îmes: '',
      ûmes: '',
      âmes: '',
      ades: '',
      eais: '',
      eons: '',
      ques: 'c',
      gues: 'g',
      nnes: 'n',
      ttes: '',
      // îtes: '',
      tés: '',
      ons: '',
      ais: '',
      ées: '',
      ees: '',
      ats: '',
      eas: '',
      ts: '',
      rs: '',
      as: '',
      es: '',
      fs: 'v',
      és: '',
      is: '',
      s: '',
      eau: '',
      au: '',
      // aire: '',
      ien: 'i'
    },

    // Transformations with 2 as minimal radix
    {
      ication: '',
      iation: '',
      ation: '',
      ateur: '',
      teur: '',
      ure: '',
      ications: '',
      iations: '',
      ateurs: '',
      ations: '',
      teurs: '',
      ures: ''
    }
  ],

  // === Step 2
  [
    {
      i: ''
    },

    {
      ent: '',
      ation: '',
      ition: '',
      tion: '',
      el: ''
    }
  ],

  // === Step 3
  [
    {
      ll: 'l',
      mm: 'm',
      nn: 'n',
      pp: 'p',
      tt: 't',
      ss: 's',
      y: '',
      t: '',
      qu: 'c'
    }
  ]
]

module.exports = steps
