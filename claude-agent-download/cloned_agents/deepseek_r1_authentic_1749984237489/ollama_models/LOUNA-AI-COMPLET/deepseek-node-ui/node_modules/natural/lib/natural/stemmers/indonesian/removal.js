/*
Copyright (c) 2017, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON> (Reimplemented from https://github.com/sastrawi/sastrawi)

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in
all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
THE SOFTWARE.
*/

// a list of commonly used words that have little meaning and can be excluded
// from analysis.

function Removal (originalWord, result, removedPart, affixType) {
  this.originalWord = originalWord
  this.result = result
  this.removedPart = removedPart
  this.affixType = affixType
}

Removal.prototype.getOriginalWord = function () {
  return this.originalWord
}

Removal.prototype.getResult = function () {
  return this.result
}

Removal.prototype.getRemovedPart = function () {
  return this.removedPart
}

Removal.prototype.getAffixType = function () {
  return this.affixType
}

module.exports = Removal
