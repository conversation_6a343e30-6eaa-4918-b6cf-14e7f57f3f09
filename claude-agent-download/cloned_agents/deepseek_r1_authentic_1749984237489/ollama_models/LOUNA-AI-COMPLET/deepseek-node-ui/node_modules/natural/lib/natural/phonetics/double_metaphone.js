/*
Copyright (c) 2011, <PERSON>

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in
all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
THE SOFTWARE.
*/

'use strict'

const Phonetic = require('./phonetic')

class DoubleMetaphone extends Phonetic {
  isVowel (c) {
    return c && c.match(/[aeiouy]/i)
  }

  process (token, maxLength) {
    // For the functions that live here in the process method
    const self = this

    token = token.toUpperCase()
    let primary = ''; let secondary = ''
    let pos = 0
    maxLength = maxLength || 32

    const san = (token.substring(0, 3) === 'SAN')
    const startsWithVowel = this.isVowel(token[0])
    const slavoGermanic = token.match(/(W|K|CZ|WITZ)/)

    if (subMatch(0, 2, ['GN', 'KN', 'PN', 'WR', 'PS'])) {
      pos++
    }

    while (pos < token.length) {
      switch (token[pos]) {
        case 'A': case 'E': case 'I': case 'O': case 'U': case 'Y':
        case 'Ê': case 'É': case 'À':
          if (pos === 0) { add('A') }
          break
        case 'B':
          addCompressedDouble('B', 'P')
          break
        case 'C':
          handleC()
          break
        case 'Ç':
          add('S')
          break
        case 'D':
          handleD()
          break
        case 'F': case 'K': case 'N':
          addCompressedDouble(token[pos])
          break
        case 'G':
          handleG()
          break
        case 'H':
          handleH()
          break
        case 'J':
          handleJ()
          break
        case 'L':
          handleL()
          break
        case 'M':
          handleM()
          break
        case 'Ñ':
          add('N')
          break
        case 'P':
          handleP()
          break
        case 'Q':
          addCompressedDouble('Q', 'K')
          break
        case 'R':
          handleR()
          break
        case 'S':
          handleS()
          break
        case 'T':
          handleT()
          break
        case 'V':
          addCompressedDouble('V', 'F')
          break
        case 'W':
          handleW()
          break
        case 'X':
          handleX()
          break
        case 'Z':
          handleZ()
          break
      }

      if (primary.length >= maxLength && secondary.length >= maxLength) {
        break
      }

      pos++
    }

    return [truncate(primary, maxLength), truncate(secondary, maxLength)]

    function subMatch (startOffset, stopOffset, terms) {
      return subMatchAbsolute(pos + startOffset, pos + stopOffset, terms)
    }

    function subMatchAbsolute (startOffset, stopOffset, terms) {
      return terms.indexOf(token.substring(startOffset, stopOffset)) > -1
    }

    function truncate (string, length) {
      if (string.length >= length) { string = string.substring(0, length) }

      return string
    }

    function addSecondary (primaryAppendage, secondaryAppendage) {
      primary += primaryAppendage
      secondary += secondaryAppendage
    }

    function add (primaryAppendage) {
      addSecondary(primaryAppendage, primaryAppendage)
    }

    function addCompressedDouble (c, encoded) {
      if (token[pos + 1] === c) {
        pos++
      }
      add(encoded || c)
    }

    function handleC () {
      if ((pos >= 1 && !self.isVowel(token[pos - 2]) &&
        token[pos - 1] === 'A' && token[pos + 1] === 'H' &&
        token[pos + 2] !== 'I') ||
        subMatch(-2, 4, ['BACHER', 'MACHER'])) {
        add('K')
        pos++
      } else if (pos === 0 && token.substring(1, 6) === 'EASAR') {
        add('S')
        add('S')
        add('R')
        pos += 6
      } else if (token.substring(pos + 1, pos + 4) === 'HIA') {
        add('K')
        pos++
      } else if (token[pos + 1] === 'H') {
        if (pos > 0 && token.substring(pos + 2, pos + 4) === 'AE') {
          addSecondary('K', 'X')
          pos++
        } else if (pos === 0 &&
                          (subMatch(1, 6, ['HARAC', 'HARIS']) ||
                              subMatch(1, 4, ['HOR', 'HUM', 'HIA', 'HEM'])) &&
                          token.substring(pos + 1, pos + 5) !== 'HORE') {
          add('K')
          pos++
        } else {
          if ((subMatchAbsolute(0, 3, ['VAN', 'VON']) || token.substring(0, 3) === 'SCH') ||
                      subMatch(-2, 4, ['ORCHES', 'ARCHIT', 'ORCHID']) ||
                      subMatch(2, 3, ['T', 'S']) ||
                      ((subMatch(-1, 0, ['A', 'O', 'U', 'E']) || pos === 0) &&
                          subMatch(2, 3, ['B', 'F', 'H', 'L', 'M', 'N', 'R', 'V', 'W']))) {
            add('K')
          } else if (pos > 0) {
            if (token.substring(0, 2) === 'MC') {
              add('K')
            } else {
              addSecondary('X', 'K')
            }
          } else {
            add('X')
          }
          pos++
        }
      } else if (token.substring(pos, pos + 2) === 'CZ' &&
                  token.substring(pos - 2, pos + 1) !== 'WICZ') {
        addSecondary('S', 'X')
        pos++
      } else if (token.substring(pos, pos + 3) === 'CIA') {
        add('X')
        pos += 2
      } else if (token[pos + 1] === 'C' && pos !== 1 && token[0] !== 'M') {
        if (['I', 'E', 'H'].indexOf(token[pos + 2]) > -1 &&
                      token.substring(pos + 2, pos + 4) !== 'HU') {
          if ((pos === 1 && token[pos - 1] === 'A') ||
                subMatch(-1, 4, ['UCCEE', 'UCCES'])) {
            add('KS')
          } else {
            add('X')
          }
          pos += 2
        } else {
          add('K')
          pos++
        }
      } else if (['K', 'G', 'Q'].indexOf(token[pos + 1]) > -1) {
        add('K')
        pos++
      } else if (['E', 'I', 'Y'].indexOf(token[pos + 1]) > -1) {
        if (subMatch(1, 3, ['IA', 'IE', 'IO'])) {
          addSecondary('S', 'X')
        } else {
          add('S')
        }
        pos++
      } else {
        add('K')
        if (token[pos + 1] === ' ' && ['C', 'Q', 'G'].indexOf(token[pos + 2])) {
          pos += 2
        } else if (['C', 'K', 'Q'].indexOf(token[pos + 1]) > -1 &&
                      !subMatch(1, 3, ['CE', 'CI'])) {
          pos++
        }
      }
    }

    function handleD () {
      if (token[pos + 1] === 'G') {
        if (['I', 'E', 'Y'].indexOf(token[pos + 2]) > -1) {
          add('J')
          pos += 2
        } else {
          add('TK')
          pos++
        }
      } else if (token[pos + 1] === 'T') {
        add('T')
        pos++
      } else { addCompressedDouble('D', 'T') }
    }

    function handleG () {
      if (token[pos + 1] === 'H') {
        if (pos > 0 && !self.isVowel(token[pos - 1])) {
          add('K')
          pos++
        } else if (pos === 0) {
          if (token[pos + 2] === 'I') {
            add('J')
          } else {
            add('K')
          }
          pos++
        } else if (pos > 1 &&
                  (['B', 'H', 'D'].indexOf(token[pos - 2]) > -1 ||
                      ['B', 'H', 'D'].indexOf(token[pos - 3]) > -1 ||
                      ['B', 'H'].indexOf(token[pos - 4]) > -1)) {
          pos++
        } else {
          if (pos > 2 &&
                          token[pos - 1] === 'U' &&
                          ['C', 'G', 'L', 'R', 'T'].indexOf(token[pos - 3]) > -1) {
            add('F')
          } else if (token[pos - 1] !== 'I') {
            add('K')
          }

          pos++
        }
      } else if (token[pos + 1] === 'N') {
        if (pos === 1 && startsWithVowel && !slavoGermanic) {
          addSecondary('KN', 'N')
        } else {
          if (token.substring(pos + 2, pos + 4) !== 'EY' &&
                          (token[pos + 1] !== 'Y' &&
                              !slavoGermanic)) {
            addSecondary('N', 'KN')
          } else { add('KN') }
        }
        pos++
      } else if (token.substring(pos + 1, pos + 3) === 'LI' && !slavoGermanic) {
        addSecondary('KL', 'L')
        pos++
      } else if (pos === 0 && (token[pos + 1] === 'Y' ||
                  subMatch(1, 3, ['ES', 'EP', 'EB', 'EL', 'EY', 'IB', 'IL', 'IN', 'IE', 'EI', 'ER']))) {
        addSecondary('K', 'J')
      } else {
        addCompressedDouble('G', 'K')
      }
    }

    function handleH () {
      // keep if starts a word or is surrounded by vowels
      if ((pos === 0 || self.isVowel(token[pos - 1])) && self.isVowel(token[pos + 1])) {
        add('H')
        pos++
      }
    }

    function handleJ () {
      const jose = (token.substring(pos + 1, pos + 4) === 'OSE')

      if (san || jose) {
        if ((pos === 0 && token[pos + 4] === ' ') ||
                      san) {
          add('H')
        } else { add('J', 'H') }
      } else {
        if (pos === 0/* && !jose */) {
          addSecondary('J', 'A')
        } else if (self.isVowel(token[pos - 1]) && !slavoGermanic &&
                      (token[pos + 1] === 'A' || token[pos + 1] === 'O')) {
          addSecondary('J', 'H')
        } else if (pos === token.length - 1) {
          addSecondary('J', ' ')
        } else { addCompressedDouble('J') }
      }
    }

    function handleL () {
      if (token[pos + 1] === 'L') {
        if (pos === token.length - 3 && (
          subMatch(-1, 3, ['ILLO', 'ILLA', 'ALLE']) || (
            token.substring(pos - 1, pos + 3) === 'ALLE' &&
            (subMatch(-2, -1, ['AS', 'OS']) > -1 ||
            ['A', 'O'].indexOf(token[token.length - 1]) > -1)))) {
          addSecondary('L', '')
          pos++
          return
        }
        pos++
      }
      add('L')
    }

    function handleM () {
      addCompressedDouble('M')
      if (token[pos - 1] === 'U' && token[pos + 1] === 'B' &&
        ((pos === token.length - 2 || token.substring(pos + 2, pos + 4) === 'ER'))) { pos++ }
    }

    function handleP () {
      if (token[pos + 1] === 'H') {
        add('F')
        pos++
      } else {
        addCompressedDouble('P')
        if (token[pos + 1] === 'B') {
          pos++
        }
      }
    }

    function handleR () {
      if (pos === token.length - 1 && !slavoGermanic &&
        token.substring(pos - 2, pos) === 'IE' &&
        !subMatch(-4, -3, ['ME', 'MA'])) {
        addSecondary('', 'R')
      } else {
        addCompressedDouble('R')
      }
    }

    function handleS () {
      if (pos === 0 && token.substring(0, 5) === 'SUGAR') {
        addSecondary('X', 'S')
      } else if (token[pos + 1] === 'H') {
        if (subMatch(2, 5, ['EIM', 'OEK', 'OLM', 'OLZ'])) {
          add('S')
        } else {
          add('X')
        }
        pos++
      } else if (subMatch(1, 3, ['IO', 'IA'])) {
        if (slavoGermanic) {
          add('S')
        } else {
          addSecondary('S', 'X')
        }
        pos++
      } else if ((pos === 0 && ['M', 'N', 'L', 'W'].indexOf(token[pos + 1]) > -1) ||
                  token[pos + 1] === 'Z') {
        addSecondary('S', 'X')
        if (token[pos + 1] === 'Z') { pos++ }
      } else if (token.substring(pos, pos + 2) === 'SC') {
        if (token[pos + 2] === 'H') {
          if (subMatch(3, 5, ['ER', 'EN'])) {
            addSecondary('X', 'SK')
          } else if (subMatch(3, 5, ['OO', 'UY', 'ED', 'EM'])) {
            add('SK')
          } else if (pos === 0 && !self.isVowel(token[3]) && token[3] !== 'W') {
            addSecondary('X', 'S')
          } else {
            add('X')
          }
        } else if (['I', 'E', 'Y'].indexOf(token[pos + 2]) > -1) {
          add('S')
        } else {
          add('SK')
        }

        pos += 2
      } else if (pos === token.length - 1 &&
                  subMatch(-2, 0, ['AI', 'OI'])) {
        addSecondary('', 'S')
      } else if (token[pos + 1] !== 'L' && (
        token[pos - 1] !== 'A' && token[pos - 1] !== 'I')) {
        addCompressedDouble('S')
        if (token[pos + 1] === 'Z') { pos++ }
      }
    }

    function handleT () {
      if (token.substring(pos + 1, pos + 4) === 'ION') {
        add('XN')
        pos += 3
      } else if (subMatch(1, 3, ['IA', 'CH'])) {
        add('X')
        pos += 2
      } else if (token[pos + 1] === 'H' ||
                  token.substring(1, 2) === 'TH') {
        if (subMatch(2, 4, ['OM', 'AM']) ||
                      ['VAN ', 'VON '].indexOf(token.substring(0, 4)) > -1 ||
                      token.substring(0, 3) === 'SCH') {
          add('T')
        } else { addSecondary('0', 'T') }
        pos++
      } else {
        addCompressedDouble('T')

        if (token[pos + 1] === 'D') { pos++ }
      }
    }

    function handleX () {
      if (pos === 0) {
        add('S')
      } else if (!(pos === token.length - 1 &&
        (['IAU', 'EAU', 'IEU'].indexOf(token.substring(pos - 3, pos)) > -1 ||
        ['AU', 'OU'].indexOf(token.substring(pos - 2, pos)) > -1))) {
        add('KS')
      }
    }

    function handleW () {
      if (pos === 0) {
        if (token[1] === 'H') {
          add('A')
        } else if (self.isVowel(token[1])) {
          addSecondary('A', 'F')
        }
      } else if (subMatch(-1, 4, ['EWSKI', 'EWSKY', 'OWSKI', 'OWSKY']) ||
        token.substring(0, 3) === 'SCH' ||
        (pos === token.length - 1 && self.isVowel(token[pos - 1]))) {
        addSecondary('', 'F')
        pos++
      } else if (['ICZ', 'ITZ'].indexOf(token.substring(pos + 1, pos + 4)) > -1) {
        addSecondary('TS', 'FX')
        pos += 3
      }
    }

    function handleZ () {
      if (token[pos + 1] === 'H') {
        add('J')
        pos++
      } else if (subMatch(1, 3, ['ZO', 'ZI', 'ZA']) ||
        (slavoGermanic && pos > 0 && token[pos - 1] !== 'T')) {
        addSecondary('S', 'TS')
        pos++
      } else { addCompressedDouble('Z', 'S') }
    }
  }

  compare (stringA, stringB) {
    const encodingsA = this.process(stringA)
    const encodingsB = this.process(stringB)

    return encodingsA[0] === encodingsB[0] ||
      encodingsA[1] === encodingsB[1]
  }
}

module.exports = DoubleMetaphone
