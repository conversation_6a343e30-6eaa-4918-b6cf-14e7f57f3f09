version: '3'

services:
  postgres:
    image: postgres:latest
    environment:
      POSTGRES_DB: naturaldb
      POSTGRES_USER: user
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"

  mongodb:
    image: mongo:latest
    environment:
      MONGO_INITDB_DATABASE: naturaldb
    ports:
      - "27017:27017"

  redis:
    image: redis:latest
    ports:
      - "6379:6379"

  memcached:
    image: memcached:latest
    ports:
      - "11211:11211"
