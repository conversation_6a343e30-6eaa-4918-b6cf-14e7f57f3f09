/*
Copyright (c) 2024, <PERSON><PERSON>

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in
all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
THE SOFTWARE.
*/

const postgres = require('pg')
require('dotenv').config()

class PostgresPlugin {
  constructor () {
    this.postgresClient = null
  }

  async configPostgres () {
    // Initialize connection to Postgres
    const client = new postgres.Client({
      user: process.env.POSTGRES_USER,
      host: process.env.POSTGRES_HOST,
      database: process.env.POSTGRES_DB,
      password: process.env.POSTGRES_PASSWORD,
      port: process.env.POSTGRES_PORT
    })
    this.postgresClient = client
    this.postgresTableName = process.env.POSTGRES_TABLE

    // Connect to the database
    await client.connect()

    // Call the function to create the table
    await this.createTableIfNotExists(client, this.postgresTableName)

    // Don't forget to close the connection when done
    // await client.end()
  }

  // Function to create the table
  async createTableIfNotExists (client, tableName) {
    // Check if the table exists
    const query = `SELECT to_regclass('${tableName}')`
    const result = await client.query(query)

    if (result.rows[0].to_regclass === null) {
      // Table does not exist, create it
      const createTableQuery = `
        CREATE TABLE ${tableName} (
          id SERIAL PRIMARY KEY,
          data JSONB
        );
      `
      await client.query(createTableQuery)
      console.log('table created')
    }
  }

  // Function to insert a JavaScript object by key
  // Returns the id generated by the database
  async store (object, options) {
    try {
      const query = `INSERT INTO ${this.postgresTableName} (data) VALUES ($1)  RETURNING id;`
      const result = await this.postgresClient.query(query, [object])
      if (result.rowCount > 0) {
        return result.rows[0].id
      } else {
        return null
      }
    } catch (error) {
      console.error('Error inserting object:', error)
    }
  }

  // Function to retrieve a JavaScript object by key
  async retrieve (key, options) {
    try {
      const query = `SELECT data FROM ${this.postgresTableName} WHERE id = $1`
      const result = await this.postgresClient.query(query, [key])
      if (result.rows.length > 0) {
        return result.rows[0].data
      } else {
        console.log('No object found with the specified key')
        return null
      }
    } catch (error) {
      console.error('Error retrieving object:', error)
      return null
    }
  }
}

module.exports = PostgresPlugin
