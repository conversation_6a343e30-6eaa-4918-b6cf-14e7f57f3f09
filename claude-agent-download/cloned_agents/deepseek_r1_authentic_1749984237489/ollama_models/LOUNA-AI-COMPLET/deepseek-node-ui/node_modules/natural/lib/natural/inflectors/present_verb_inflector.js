/*
Copyright (c) 2011, <PERSON>

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in
all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
THE SOFTWARE.
*/

const SingularPluralInflector = require('./singular_plural_inflector')
const FormSet = require('./form_set')

class VerbInflector extends SingularPluralInflector {
  constructor () {
    super()
    this.ambiguous = [
      'will'
    ]

    // this.attach = attach

    this.customPluralForms = []
    this.customSingularForms = []
    this.singularForms = new FormSet()
    this.pluralForms = new FormSet()

    this.addIrregular('am', 'are')
    this.addIrregular('is', 'are')
    this.addIrregular('was', 'were')
    this.addIrregular('has', 'have')

    this.singularForms.regularForms.push([/ed$/i, 'ed'])
    this.singularForms.regularForms.push([/ss$/i, 'sses'])
    this.singularForms.regularForms.push([/x$/i, 'xes'])
    this.singularForms.regularForms.push([/(h|z|o)$/i, '$1es'])
    this.singularForms.regularForms.push([/$zz/i, 'zzes'])
    this.singularForms.regularForms.push([/([^a|e|i|o|u])y$/i, '$1ies'])
    this.singularForms.regularForms.push([/$/i, 's'])

    this.pluralForms.regularForms.push([/sses$/i, 'ss'])
    this.pluralForms.regularForms.push([/xes$/i, 'x'])
    this.pluralForms.regularForms.push([/([cs])hes$/i, '$1h'])
    this.pluralForms.regularForms.push([/zzes$/i, 'zz'])
    this.pluralForms.regularForms.push([/([^h|z|o|i])es$/i, '$1e'])
    this.pluralForms.regularForms.push([/ies$/i, 'y'])// flies->fly
    this.pluralForms.regularForms.push([/e?s$/i, ''])
  }
}

module.exports = VerbInflector
