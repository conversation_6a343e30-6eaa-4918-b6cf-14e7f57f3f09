{"version": 3, "file": "css-calc.js", "sources": ["../../../src/js/css-calc.ts"], "sourcesContent": ["/**\n * css-calc\n */\n\nimport { calc } from '@csstools/css-calc';\nimport { CSSToken, TokenType, tokenize } from '@csstools/css-tokenizer';\nimport {\n  CacheItem,\n  NullObject,\n  createCacheKey,\n  getCache,\n  setCache\n} from './cache';\nimport { isString, isStringOrNumber } from './common';\nimport { resolveVar } from './css-var';\nimport { roundToPrecision } from './util';\nimport { MatchedRegExp, Options } from './typedef';\n\n/* constants */\nimport {\n  ANGLE,\n  LENGTH,\n  NUM,\n  SYN_FN_CALC,\n  SYN_FN_MATH_START,\n  SYN_FN_VAR,\n  SYN_FN_VAR_START,\n  VAL_SPEC\n} from './constant';\nconst {\n  CloseParen: PAREN_CLOSE,\n  Comment: COMMENT,\n  Dimension: DIM,\n  EOF,\n  Function: FUNC,\n  OpenParen: PAREN_OPEN,\n  Whitespace: W_SPACE\n} = TokenType;\nconst NAMESPACE = 'css-calc';\n\n/* numeric constants */\nconst TRIA = 3;\nconst HEX = 16;\nconst MAX_PCT = 100;\n\n/* regexp */\nconst REG_FN_CALC = new RegExp(SYN_FN_CALC);\nconst REG_FN_CALC_NUM = new RegExp(`^calc\\\\((${NUM})\\\\)$`);\nconst REG_FN_MATH_START = new RegExp(SYN_FN_MATH_START);\nconst REG_FN_VAR = new RegExp(SYN_FN_VAR);\nconst REG_FN_VAR_START = new RegExp(SYN_FN_VAR_START);\nconst REG_OPERATOR = /\\s[*+/-]\\s/;\nconst REG_TYPE_DIM = new RegExp(`^(${NUM})(${ANGLE}|${LENGTH})$`);\nconst REG_TYPE_DIM_PCT = new RegExp(`^(${NUM})(${ANGLE}|${LENGTH}|%)$`);\nconst REG_TYPE_PCT = new RegExp(`^(${NUM})%$`);\n\n/**\n * Calclator\n */\nexport class Calculator {\n  /* private */\n  // number\n  #hasNum: boolean;\n  #numSum: number[];\n  #numMul: number[];\n  // percentage\n  #hasPct: boolean;\n  #pctSum: number[];\n  #pctMul: number[];\n  // dimension\n  #hasDim: boolean;\n  #dimSum: string[];\n  #dimSub: string[];\n  #dimMul: string[];\n  #dimDiv: string[];\n  // et cetra\n  #hasEtc: boolean;\n  #etcSum: string[];\n  #etcSub: string[];\n  #etcMul: string[];\n  #etcDiv: string[];\n\n  /**\n   * constructor\n   */\n  constructor() {\n    // number\n    this.#hasNum = false;\n    this.#numSum = [];\n    this.#numMul = [];\n    // percentage\n    this.#hasPct = false;\n    this.#pctSum = [];\n    this.#pctMul = [];\n    // dimension\n    this.#hasDim = false;\n    this.#dimSum = [];\n    this.#dimSub = [];\n    this.#dimMul = [];\n    this.#dimDiv = [];\n    // et cetra\n    this.#hasEtc = false;\n    this.#etcSum = [];\n    this.#etcSub = [];\n    this.#etcMul = [];\n    this.#etcDiv = [];\n  }\n\n  get hasNum() {\n    return this.#hasNum;\n  }\n\n  set hasNum(value: boolean) {\n    this.#hasNum = !!value;\n  }\n\n  get numSum() {\n    return this.#numSum;\n  }\n\n  get numMul() {\n    return this.#numMul;\n  }\n\n  get hasPct() {\n    return this.#hasPct;\n  }\n\n  set hasPct(value: boolean) {\n    this.#hasPct = !!value;\n  }\n\n  get pctSum() {\n    return this.#pctSum;\n  }\n\n  get pctMul() {\n    return this.#pctMul;\n  }\n\n  get hasDim() {\n    return this.#hasDim;\n  }\n\n  set hasDim(value: boolean) {\n    this.#hasDim = !!value;\n  }\n\n  get dimSum() {\n    return this.#dimSum;\n  }\n\n  get dimSub() {\n    return this.#dimSub;\n  }\n\n  get dimMul() {\n    return this.#dimMul;\n  }\n\n  get dimDiv() {\n    return this.#dimDiv;\n  }\n\n  get hasEtc() {\n    return this.#hasEtc;\n  }\n\n  set hasEtc(value: boolean) {\n    this.#hasEtc = !!value;\n  }\n\n  get etcSum() {\n    return this.#etcSum;\n  }\n\n  get etcSub() {\n    return this.#etcSub;\n  }\n\n  get etcMul() {\n    return this.#etcMul;\n  }\n\n  get etcDiv() {\n    return this.#etcDiv;\n  }\n\n  /**\n   * clear values\n   * @returns void\n   */\n  clear() {\n    // number\n    this.#hasNum = false;\n    this.#numSum = [];\n    this.#numMul = [];\n    // percentage\n    this.#hasPct = false;\n    this.#pctSum = [];\n    this.#pctMul = [];\n    // dimension\n    this.#hasDim = false;\n    this.#dimSum = [];\n    this.#dimSub = [];\n    this.#dimMul = [];\n    this.#dimDiv = [];\n    // et cetra\n    this.#hasEtc = false;\n    this.#etcSum = [];\n    this.#etcSub = [];\n    this.#etcMul = [];\n    this.#etcDiv = [];\n  }\n\n  /**\n   * sort values\n   * @param values - values\n   * @returns sorted values\n   */\n  sort(values: string[] = []): string[] {\n    const arr = [...values];\n    if (arr.length > 1) {\n      arr.sort((a, b) => {\n        let res;\n        if (REG_TYPE_DIM_PCT.test(a) && REG_TYPE_DIM_PCT.test(b)) {\n          const [, valA, unitA] = a.match(REG_TYPE_DIM_PCT) as MatchedRegExp;\n          const [, valB, unitB] = b.match(REG_TYPE_DIM_PCT) as MatchedRegExp;\n          if (unitA === unitB) {\n            if (Number(valA) === Number(valB)) {\n              res = 0;\n            } else if (Number(valA) > Number(valB)) {\n              res = 1;\n            } else {\n              res = -1;\n            }\n          } else if (unitA > unitB) {\n            res = 1;\n          } else {\n            res = -1;\n          }\n        } else {\n          if (a === b) {\n            res = 0;\n          } else if (a > b) {\n            res = 1;\n          } else {\n            res = -1;\n          }\n        }\n        return res;\n      });\n    }\n    return arr;\n  }\n\n  /**\n   * multiply values\n   * @returns resolved value\n   */\n  multiply(): string {\n    const value = [];\n    let num;\n    if (this.#hasNum) {\n      num = 1;\n      for (const i of this.#numMul) {\n        num *= i;\n        if (num === 0 || !Number.isFinite(num) || Number.isNaN(num)) {\n          break;\n        }\n      }\n      if (!this.#hasPct && !this.#hasDim && !this.hasEtc) {\n        if (Number.isFinite(num)) {\n          num = roundToPrecision(num, HEX);\n        }\n        value.push(num);\n      }\n    }\n    if (this.#hasPct) {\n      if (typeof num !== 'number') {\n        num = 1;\n      }\n      for (const i of this.#pctMul) {\n        num *= i;\n        if (num === 0 || !Number.isFinite(num) || Number.isNaN(num)) {\n          break;\n        }\n      }\n      if (Number.isFinite(num)) {\n        num = `${roundToPrecision(num, HEX)}%`;\n      }\n      if (!this.#hasDim && !this.hasEtc) {\n        value.push(num);\n      }\n    }\n    if (this.#hasDim) {\n      let dim = '';\n      let mul = '';\n      let div = '';\n      if (this.#dimMul.length) {\n        if (this.#dimMul.length === 1) {\n          [mul] = this.#dimMul as [string];\n        } else {\n          mul = `${this.sort(this.#dimMul).join(' * ')}`;\n        }\n      }\n      if (this.#dimDiv.length) {\n        if (this.#dimDiv.length === 1) {\n          [div] = this.#dimDiv as [string];\n        } else {\n          div = `${this.sort(this.#dimDiv).join(' * ')}`;\n        }\n      }\n      if (Number.isFinite(num)) {\n        if (mul) {\n          if (div) {\n            if (div.includes('*')) {\n              dim = calc(`calc(${num} * ${mul} / (${div}))`, {\n                toCanonicalUnits: true\n              });\n            } else {\n              dim = calc(`calc(${num} * ${mul} / ${div})`, {\n                toCanonicalUnits: true\n              });\n            }\n          } else {\n            dim = calc(`calc(${num} * ${mul})`, {\n              toCanonicalUnits: true\n            });\n          }\n        } else if (div.includes('*')) {\n          dim = calc(`calc(${num} / (${div}))`, {\n            toCanonicalUnits: true\n          });\n        } else {\n          dim = calc(`calc(${num} / ${div})`, {\n            toCanonicalUnits: true\n          });\n        }\n        value.push(dim.replace(/^calc/, ''));\n      } else {\n        if (!value.length && num !== undefined) {\n          value.push(num);\n        }\n        if (mul) {\n          if (div) {\n            if (div.includes('*')) {\n              dim = calc(`calc(${mul} / (${div}))`, {\n                toCanonicalUnits: true\n              });\n            } else {\n              dim = calc(`calc(${mul} / ${div})`, {\n                toCanonicalUnits: true\n              });\n            }\n          } else {\n            dim = calc(`calc(${mul})`, {\n              toCanonicalUnits: true\n            });\n          }\n          if (value.length) {\n            value.push('*', dim.replace(/^calc/, ''));\n          } else {\n            value.push(dim.replace(/^calc/, ''));\n          }\n        } else {\n          dim = calc(`calc(${div})`, {\n            toCanonicalUnits: true\n          });\n          if (value.length) {\n            value.push('/', dim.replace(/^calc/, ''));\n          } else {\n            value.push('1', '/', dim.replace(/^calc/, ''));\n          }\n        }\n      }\n    }\n    if (this.#hasEtc) {\n      if (this.#etcMul.length) {\n        if (!value.length && num !== undefined) {\n          value.push(num);\n        }\n        const mul = this.sort(this.#etcMul).join(' * ');\n        if (value.length) {\n          value.push(`* ${mul}`);\n        } else {\n          value.push(`${mul}`);\n        }\n      }\n      if (this.#etcDiv.length) {\n        const div = this.sort(this.#etcDiv).join(' * ');\n        if (div.includes('*')) {\n          if (value.length) {\n            value.push(`/ (${div})`);\n          } else {\n            value.push(`1 / (${div})`);\n          }\n        } else if (value.length) {\n          value.push(`/ ${div}`);\n        } else {\n          value.push(`1 / ${div}`);\n        }\n      }\n    }\n    if (value.length) {\n      return value.join(' ');\n    }\n    return '';\n  }\n\n  /**\n   * sum values\n   * @returns resolved value\n   */\n  sum(): string {\n    const value = [];\n    if (this.#hasNum) {\n      let num = 0;\n      for (const i of this.#numSum) {\n        num += i;\n        if (!Number.isFinite(num) || Number.isNaN(num)) {\n          break;\n        }\n      }\n      value.push(num);\n    }\n    if (this.#hasPct) {\n      let num: number | string = 0;\n      for (const i of this.#pctSum) {\n        num += i;\n        if (!Number.isFinite(num)) {\n          break;\n        }\n      }\n      if (Number.isFinite(num)) {\n        num = `${num}%`;\n      }\n      if (value.length) {\n        value.push(`+ ${num}`);\n      } else {\n        value.push(num);\n      }\n    }\n    if (this.#hasDim) {\n      let dim, sum, sub;\n      if (this.#dimSum.length) {\n        sum = this.sort(this.#dimSum).join(' + ');\n      }\n      if (this.#dimSub.length) {\n        sub = this.sort(this.#dimSub).join(' + ');\n      }\n      if (sum) {\n        if (sub) {\n          if (sub.includes('-')) {\n            dim = calc(`calc(${sum} - (${sub}))`, {\n              toCanonicalUnits: true\n            });\n          } else {\n            dim = calc(`calc(${sum} - ${sub})`, {\n              toCanonicalUnits: true\n            });\n          }\n        } else {\n          dim = calc(`calc(${sum})`, {\n            toCanonicalUnits: true\n          });\n        }\n      } else {\n        dim = calc(`calc(-1 * (${sub}))`, {\n          toCanonicalUnits: true\n        });\n      }\n      if (value.length) {\n        value.push('+', dim.replace(/^calc/, ''));\n      } else {\n        value.push(dim.replace(/^calc/, ''));\n      }\n    }\n    if (this.#hasEtc) {\n      if (this.#etcSum.length) {\n        const sum = this.sort(this.#etcSum)\n          .map(item => {\n            let res;\n            if (\n              REG_OPERATOR.test(item) &&\n              !item.startsWith('(') &&\n              !item.endsWith(')')\n            ) {\n              res = `(${item})`;\n            } else {\n              res = item;\n            }\n            return res;\n          })\n          .join(' + ');\n        if (value.length) {\n          if (this.#etcSum.length > 1) {\n            value.push(`+ (${sum})`);\n          } else {\n            value.push(`+ ${sum}`);\n          }\n        } else {\n          value.push(`${sum}`);\n        }\n      }\n      if (this.#etcSub.length) {\n        const sub = this.sort(this.#etcSub)\n          .map(item => {\n            let res;\n            if (\n              REG_OPERATOR.test(item) &&\n              !item.startsWith('(') &&\n              !item.endsWith(')')\n            ) {\n              res = `(${item})`;\n            } else {\n              res = item;\n            }\n            return res;\n          })\n          .join(' + ');\n        if (value.length) {\n          if (this.#etcSub.length > 1) {\n            value.push(`- (${sub})`);\n          } else {\n            value.push(`- ${sub}`);\n          }\n        } else if (this.#etcSub.length > 1) {\n          value.push(`-1 * (${sub})`);\n        } else {\n          value.push(`-1 * ${sub}`);\n        }\n      }\n    }\n    if (value.length) {\n      return value.join(' ');\n    }\n    return '';\n  }\n}\n\n/**\n * sort calc values\n * @param values - values to sort\n * @param [finalize] - finalize values\n * @returns sorted values\n */\nexport const sortCalcValues = (\n  values: (number | string)[] = [],\n  finalize: boolean = false\n): string => {\n  if (values.length < TRIA) {\n    throw new Error(`Unexpected array length ${values.length}.`);\n  }\n  const start = values.shift();\n  if (!isString(start) || !start.endsWith('(')) {\n    throw new Error(`Unexpected token ${start}.`);\n  }\n  const end = values.pop();\n  if (end !== ')') {\n    throw new Error(`Unexpected token ${end}.`);\n  }\n  if (values.length === 1) {\n    const [value] = values;\n    if (!isStringOrNumber(value)) {\n      throw new Error(`Unexpected token ${value}.`);\n    }\n    return `${start}${value}${end}`;\n  }\n  const sortedValues = [];\n  const cal = new Calculator();\n  let operator: string = '';\n  const l = values.length;\n  for (let i = 0; i < l; i++) {\n    const value = values[i];\n    if (!isStringOrNumber(value)) {\n      throw new Error(`Unexpected token ${value}.`);\n    }\n    if (value === '*' || value === '/') {\n      operator = value;\n    } else if (value === '+' || value === '-') {\n      const sortedValue = cal.multiply();\n      if (sortedValue) {\n        sortedValues.push(sortedValue, value);\n      }\n      cal.clear();\n      operator = '';\n    } else {\n      const numValue = Number(value);\n      const strValue = `${value}`;\n      switch (operator) {\n        case '/': {\n          if (Number.isFinite(numValue)) {\n            cal.hasNum = true;\n            cal.numMul.push(1 / numValue);\n          } else if (REG_TYPE_PCT.test(strValue)) {\n            const [, val] = strValue.match(REG_TYPE_PCT) as MatchedRegExp;\n            cal.hasPct = true;\n            cal.pctMul.push((MAX_PCT * MAX_PCT) / Number(val));\n          } else if (REG_TYPE_DIM.test(strValue)) {\n            cal.hasDim = true;\n            cal.dimDiv.push(strValue);\n          } else {\n            cal.hasEtc = true;\n            cal.etcDiv.push(strValue);\n          }\n          break;\n        }\n        case '*':\n        default: {\n          if (Number.isFinite(numValue)) {\n            cal.hasNum = true;\n            cal.numMul.push(numValue);\n          } else if (REG_TYPE_PCT.test(strValue)) {\n            const [, val] = strValue.match(REG_TYPE_PCT) as MatchedRegExp;\n            cal.hasPct = true;\n            cal.pctMul.push(Number(val));\n          } else if (REG_TYPE_DIM.test(strValue)) {\n            cal.hasDim = true;\n            cal.dimMul.push(strValue);\n          } else {\n            cal.hasEtc = true;\n            cal.etcMul.push(strValue);\n          }\n        }\n      }\n    }\n    if (i === l - 1) {\n      const sortedValue = cal.multiply();\n      if (sortedValue) {\n        sortedValues.push(sortedValue);\n      }\n      cal.clear();\n      operator = '';\n    }\n  }\n  let resolvedValue = '';\n  if (finalize && (sortedValues.includes('+') || sortedValues.includes('-'))) {\n    const finalizedValues = [];\n    cal.clear();\n    operator = '';\n    const l = sortedValues.length;\n    for (let i = 0; i < l; i++) {\n      const value = sortedValues[i];\n      if (isStringOrNumber(value)) {\n        if (value === '+' || value === '-') {\n          operator = value;\n        } else {\n          const numValue = Number(value);\n          const strValue = `${value}`;\n          switch (operator) {\n            case '-': {\n              if (Number.isFinite(numValue)) {\n                cal.hasNum = true;\n                cal.numSum.push(-1 * numValue);\n              } else if (REG_TYPE_PCT.test(strValue)) {\n                const [, val] = strValue.match(REG_TYPE_PCT) as MatchedRegExp;\n                cal.hasPct = true;\n                cal.pctSum.push(-1 * Number(val));\n              } else if (REG_TYPE_DIM.test(strValue)) {\n                cal.hasDim = true;\n                cal.dimSub.push(strValue);\n              } else {\n                cal.hasEtc = true;\n                cal.etcSub.push(strValue);\n              }\n              break;\n            }\n            case '+':\n            default: {\n              if (Number.isFinite(numValue)) {\n                cal.hasNum = true;\n                cal.numSum.push(numValue);\n              } else if (REG_TYPE_PCT.test(strValue)) {\n                const [, val] = strValue.match(REG_TYPE_PCT) as MatchedRegExp;\n                cal.hasPct = true;\n                cal.pctSum.push(Number(val));\n              } else if (REG_TYPE_DIM.test(strValue)) {\n                cal.hasDim = true;\n                cal.dimSum.push(strValue);\n              } else {\n                cal.hasEtc = true;\n                cal.etcSum.push(strValue);\n              }\n            }\n          }\n        }\n      }\n      if (i === l - 1) {\n        const sortedValue = cal.sum();\n        if (sortedValue) {\n          finalizedValues.push(sortedValue);\n        }\n        cal.clear();\n        operator = '';\n      }\n    }\n    resolvedValue = finalizedValues.join(' ').replace(/\\+\\s-/g, '- ');\n  } else {\n    resolvedValue = sortedValues.join(' ').replace(/\\+\\s-/g, '- ');\n  }\n  if (\n    resolvedValue.startsWith('(') &&\n    resolvedValue.endsWith(')') &&\n    resolvedValue.lastIndexOf('(') === 0 &&\n    resolvedValue.indexOf(')') === resolvedValue.length - 1\n  ) {\n    resolvedValue = resolvedValue.replace(/^\\(/, '').replace(/\\)$/, '');\n  }\n  return `${start}${resolvedValue}${end}`;\n};\n\n/**\n * serialize calc\n * @param value - CSS value\n * @param [opt] - options\n * @returns serialized value\n */\nexport const serializeCalc = (value: string, opt: Options = {}): string => {\n  const { format = '' } = opt;\n  if (isString(value)) {\n    if (!REG_FN_VAR_START.test(value) || format !== VAL_SPEC) {\n      return value;\n    }\n    value = value.toLowerCase().trim();\n  } else {\n    throw new TypeError(`${value} is not a string.`);\n  }\n  const cacheKey: string = createCacheKey(\n    {\n      namespace: NAMESPACE,\n      name: 'serializeCalc',\n      value\n    },\n    opt\n  );\n  const cachedResult = getCache(cacheKey);\n  if (cachedResult instanceof CacheItem) {\n    return cachedResult.item as string;\n  }\n  const items: string[] = tokenize({ css: value })\n    .map((token: CSSToken): string => {\n      const [type, value] = token as [TokenType, string];\n      let res = '';\n      if (type !== W_SPACE && type !== COMMENT) {\n        res = value;\n      }\n      return res;\n    })\n    .filter(v => v);\n  let startIndex = items.findLastIndex((item: string) => /\\($/.test(item));\n  while (startIndex) {\n    const endIndex = items.findIndex((item: unknown, index: number) => {\n      return item === ')' && index > startIndex;\n    });\n    const slicedValues: string[] = items.slice(startIndex, endIndex + 1);\n    let serializedValue: string = sortCalcValues(slicedValues);\n    if (REG_FN_VAR_START.test(serializedValue)) {\n      serializedValue = calc(serializedValue, {\n        toCanonicalUnits: true\n      });\n    }\n    items.splice(startIndex, endIndex - startIndex + 1, serializedValue);\n    startIndex = items.findLastIndex((item: string) => /\\($/.test(item));\n  }\n  const serializedCalc = sortCalcValues(items, true);\n  setCache(cacheKey, serializedCalc);\n  return serializedCalc;\n};\n\n/**\n * resolve dimension\n * @param token - CSS token\n * @param [opt] - options\n * @returns resolved value\n */\nexport const resolveDimension = (\n  token: CSSToken,\n  opt: Options = {}\n): string | NullObject => {\n  if (!Array.isArray(token)) {\n    throw new TypeError(`${token} is not an array.`);\n  }\n  const [, , , , detail = {}] = token;\n  const { unit, value } = detail as {\n    unit: string;\n    value: number;\n  };\n  const { dimension = {} } = opt;\n  if (unit === 'px') {\n    return `${value}${unit}`;\n  }\n  const relativeValue = Number(value);\n  if (unit && Number.isFinite(relativeValue)) {\n    let pixelValue;\n    if (Object.hasOwnProperty.call(dimension, unit)) {\n      pixelValue = dimension[unit];\n    } else if (typeof dimension.callback === 'function') {\n      pixelValue = dimension.callback(unit);\n    }\n    pixelValue = Number(pixelValue);\n    if (Number.isFinite(pixelValue)) {\n      return `${relativeValue * pixelValue}px`;\n    }\n  }\n  return new NullObject();\n};\n\n/**\n * parse tokens\n * @param tokens - CSS tokens\n * @param [opt] - options\n * @returns parsed tokens\n */\nexport const parseTokens = (\n  tokens: CSSToken[],\n  opt: Options = {}\n): string[] => {\n  if (!Array.isArray(tokens)) {\n    throw new TypeError(`${tokens} is not an array.`);\n  }\n  const { format = '' } = opt;\n  const mathFunc = new Set();\n  let nest = 0;\n  const res: string[] = [];\n  while (tokens.length) {\n    const token = tokens.shift();\n    if (!Array.isArray(token)) {\n      throw new TypeError(`${token} is not an array.`);\n    }\n    const [type = '', value = ''] = token as [TokenType, string];\n    switch (type) {\n      case DIM: {\n        if (format === VAL_SPEC && !mathFunc.has(nest)) {\n          res.push(value);\n        } else {\n          const resolvedValue = resolveDimension(token, opt);\n          if (isString(resolvedValue)) {\n            res.push(resolvedValue);\n          } else {\n            res.push(value);\n          }\n        }\n        break;\n      }\n      case FUNC:\n      case PAREN_OPEN: {\n        res.push(value);\n        nest++;\n        if (REG_FN_MATH_START.test(value)) {\n          mathFunc.add(nest);\n        }\n        break;\n      }\n      case PAREN_CLOSE: {\n        if (res.length) {\n          const lastValue = res[res.length - 1];\n          if (lastValue === ' ') {\n            res.splice(-1, 1, value);\n          } else {\n            res.push(value);\n          }\n        } else {\n          res.push(value);\n        }\n        if (mathFunc.has(nest)) {\n          mathFunc.delete(nest);\n        }\n        nest--;\n        break;\n      }\n      case W_SPACE: {\n        if (res.length) {\n          const lastValue = res[res.length - 1];\n          if (\n            isString(lastValue) &&\n            !lastValue.endsWith('(') &&\n            lastValue !== ' '\n          ) {\n            res.push(value);\n          }\n        }\n        break;\n      }\n      default: {\n        if (type !== COMMENT && type !== EOF) {\n          res.push(value);\n        }\n      }\n    }\n  }\n  return res;\n};\n\n/**\n * CSS calc()\n * @param value - CSS value including calc()\n * @param [opt] - options\n * @returns resolved value\n */\nexport const cssCalc = (value: string, opt: Options = {}): string => {\n  const { format = '' } = opt;\n  if (isString(value)) {\n    if (REG_FN_VAR.test(value)) {\n      if (format === VAL_SPEC) {\n        return value;\n      } else {\n        const resolvedValue = resolveVar(value, opt);\n        if (isString(resolvedValue)) {\n          return resolvedValue;\n        } else {\n          return '';\n        }\n      }\n    } else if (!REG_FN_CALC.test(value)) {\n      return value;\n    }\n    value = value.toLowerCase().trim();\n  } else {\n    throw new TypeError(`${value} is not a string.`);\n  }\n  const cacheKey: string = createCacheKey(\n    {\n      namespace: NAMESPACE,\n      name: 'cssCalc',\n      value\n    },\n    opt\n  );\n  const cachedResult = getCache(cacheKey);\n  if (cachedResult instanceof CacheItem) {\n    return cachedResult.item as string;\n  }\n  const tokens = tokenize({ css: value });\n  const values = parseTokens(tokens, opt);\n  let resolvedValue: string = calc(values.join(''), {\n    toCanonicalUnits: true\n  });\n  if (REG_FN_VAR_START.test(value)) {\n    if (REG_TYPE_DIM_PCT.test(resolvedValue)) {\n      const [, val, unit] = resolvedValue.match(\n        REG_TYPE_DIM_PCT\n      ) as MatchedRegExp;\n      resolvedValue = `${roundToPrecision(Number(val), HEX)}${unit}`;\n    }\n    // wrap with `calc()`\n    if (\n      resolvedValue &&\n      !REG_FN_VAR_START.test(resolvedValue) &&\n      format === VAL_SPEC\n    ) {\n      resolvedValue = `calc(${resolvedValue})`;\n    }\n  }\n  if (format === VAL_SPEC) {\n    if (/\\s[-+*/]\\s/.test(resolvedValue) && !resolvedValue.includes('NaN')) {\n      resolvedValue = serializeCalc(resolvedValue, opt);\n    } else if (REG_FN_CALC_NUM.test(resolvedValue)) {\n      const [, val] = resolvedValue.match(REG_FN_CALC_NUM) as MatchedRegExp;\n      resolvedValue = `calc(${roundToPrecision(Number(val), HEX)})`;\n    }\n  }\n  setCache(cacheKey, resolvedValue);\n  return resolvedValue;\n};\n"], "names": ["l", "value", "resolvedValue"], "mappings": ";;;;;;;;;;;;;;;AA6BA,MAAM;AAAA,EACJ,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,WAAW;AAAA,EACX;AAAA,EACA,UAAU;AAAA,EACV,WAAW;AAAA,EACX,YAAY;AACd,IAAI;AACJ,MAAM,YAAY;AAGlB,MAAM,OAAO;AACb,MAAM,MAAM;AACZ,MAAM,UAAU;AAGhB,MAAM,cAAc,IAAI,OAAO,WAAW;AAC1C,MAAM,kBAAkB,IAAI,OAAO,YAAY,GAAG,OAAO;AACzD,MAAM,oBAAoB,IAAI,OAAO,iBAAiB;AACtD,MAAM,aAAa,IAAI,OAAO,UAAU;AACxC,MAAM,mBAAmB,IAAI,OAAO,gBAAgB;AACpD,MAAM,eAAe;AACrB,MAAM,eAAe,IAAI,OAAO,KAAK,GAAG,KAAK,KAAK,IAAI,MAAM,IAAI;AAChE,MAAM,mBAAmB,IAAI,OAAO,KAAK,GAAG,KAAK,KAAK,IAAI,MAAM,MAAM;AACtE,MAAM,eAAe,IAAI,OAAO,KAAK,GAAG,KAAK;AAKtC,MAAM,WAAW;AAAA;AAAA;AAAA;AAAA,EA0BtB,cAAc;AAvBd;AAAA;AAAA;AACA;AACA;AAEA;AAAA;AACA;AACA;AAEA;AAAA;AACA;AACA;AACA;AACA;AAEA;AAAA;AACA;AACA;AACA;AACA;AAOE,uBAAK,SAAU;AACf,uBAAK,SAAU,CAAC;AAChB,uBAAK,SAAU,CAAC;AAEhB,uBAAK,SAAU;AACf,uBAAK,SAAU,CAAC;AAChB,uBAAK,SAAU,CAAC;AAEhB,uBAAK,SAAU;AACf,uBAAK,SAAU,CAAC;AAChB,uBAAK,SAAU,CAAC;AAChB,uBAAK,SAAU,CAAC;AAChB,uBAAK,SAAU,CAAC;AAEhB,uBAAK,SAAU;AACf,uBAAK,SAAU,CAAC;AAChB,uBAAK,SAAU,CAAC;AAChB,uBAAK,SAAU,CAAC;AAChB,uBAAK,SAAU,CAAC;AAAA,EAAA;AAAA,EAGlB,IAAI,SAAS;AACX,WAAO,mBAAK;AAAA,EAAA;AAAA,EAGd,IAAI,OAAO,OAAgB;AACpB,uBAAA,SAAU,CAAC,CAAC;AAAA,EAAA;AAAA,EAGnB,IAAI,SAAS;AACX,WAAO,mBAAK;AAAA,EAAA;AAAA,EAGd,IAAI,SAAS;AACX,WAAO,mBAAK;AAAA,EAAA;AAAA,EAGd,IAAI,SAAS;AACX,WAAO,mBAAK;AAAA,EAAA;AAAA,EAGd,IAAI,OAAO,OAAgB;AACpB,uBAAA,SAAU,CAAC,CAAC;AAAA,EAAA;AAAA,EAGnB,IAAI,SAAS;AACX,WAAO,mBAAK;AAAA,EAAA;AAAA,EAGd,IAAI,SAAS;AACX,WAAO,mBAAK;AAAA,EAAA;AAAA,EAGd,IAAI,SAAS;AACX,WAAO,mBAAK;AAAA,EAAA;AAAA,EAGd,IAAI,OAAO,OAAgB;AACpB,uBAAA,SAAU,CAAC,CAAC;AAAA,EAAA;AAAA,EAGnB,IAAI,SAAS;AACX,WAAO,mBAAK;AAAA,EAAA;AAAA,EAGd,IAAI,SAAS;AACX,WAAO,mBAAK;AAAA,EAAA;AAAA,EAGd,IAAI,SAAS;AACX,WAAO,mBAAK;AAAA,EAAA;AAAA,EAGd,IAAI,SAAS;AACX,WAAO,mBAAK;AAAA,EAAA;AAAA,EAGd,IAAI,SAAS;AACX,WAAO,mBAAK;AAAA,EAAA;AAAA,EAGd,IAAI,OAAO,OAAgB;AACpB,uBAAA,SAAU,CAAC,CAAC;AAAA,EAAA;AAAA,EAGnB,IAAI,SAAS;AACX,WAAO,mBAAK;AAAA,EAAA;AAAA,EAGd,IAAI,SAAS;AACX,WAAO,mBAAK;AAAA,EAAA;AAAA,EAGd,IAAI,SAAS;AACX,WAAO,mBAAK;AAAA,EAAA;AAAA,EAGd,IAAI,SAAS;AACX,WAAO,mBAAK;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOd,QAAQ;AAEN,uBAAK,SAAU;AACf,uBAAK,SAAU,CAAC;AAChB,uBAAK,SAAU,CAAC;AAEhB,uBAAK,SAAU;AACf,uBAAK,SAAU,CAAC;AAChB,uBAAK,SAAU,CAAC;AAEhB,uBAAK,SAAU;AACf,uBAAK,SAAU,CAAC;AAChB,uBAAK,SAAU,CAAC;AAChB,uBAAK,SAAU,CAAC;AAChB,uBAAK,SAAU,CAAC;AAEhB,uBAAK,SAAU;AACf,uBAAK,SAAU,CAAC;AAChB,uBAAK,SAAU,CAAC;AAChB,uBAAK,SAAU,CAAC;AAChB,uBAAK,SAAU,CAAC;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQlB,KAAK,SAAmB,IAAc;AAC9B,UAAA,MAAM,CAAC,GAAG,MAAM;AAClB,QAAA,IAAI,SAAS,GAAG;AACd,UAAA,KAAK,CAAC,GAAG,MAAM;AACb,YAAA;AACJ,YAAI,iBAAiB,KAAK,CAAC,KAAK,iBAAiB,KAAK,CAAC,GAAG;AACxD,gBAAM,CAAA,EAAG,MAAM,KAAK,IAAI,EAAE,MAAM,gBAAgB;AAChD,gBAAM,CAAA,EAAG,MAAM,KAAK,IAAI,EAAE,MAAM,gBAAgB;AAChD,cAAI,UAAU,OAAO;AACnB,gBAAI,OAAO,IAAI,MAAM,OAAO,IAAI,GAAG;AAC3B,oBAAA;AAAA,uBACG,OAAO,IAAI,IAAI,OAAO,IAAI,GAAG;AAChC,oBAAA;AAAA,YAAA,OACD;AACC,oBAAA;AAAA,YAAA;AAAA,UACR,WACS,QAAQ,OAAO;AAClB,kBAAA;AAAA,UAAA,OACD;AACC,kBAAA;AAAA,UAAA;AAAA,QACR,OACK;AACL,cAAI,MAAM,GAAG;AACL,kBAAA;AAAA,UAAA,WACG,IAAI,GAAG;AACV,kBAAA;AAAA,UAAA,OACD;AACC,kBAAA;AAAA,UAAA;AAAA,QACR;AAEK,eAAA;AAAA,MAAA,CACR;AAAA,IAAA;AAEI,WAAA;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOT,WAAmB;AACjB,UAAM,QAAQ,CAAC;AACX,QAAA;AACJ,QAAI,mBAAK,UAAS;AACV,YAAA;AACK,iBAAA,KAAK,mBAAK,UAAS;AACrB,eAAA;AACH,YAAA,QAAQ,KAAK,CAAC,OAAO,SAAS,GAAG,KAAK,OAAO,MAAM,GAAG,GAAG;AAC3D;AAAA,QAAA;AAAA,MACF;AAEE,UAAA,CAAC,mBAAK,YAAW,CAAC,mBAAK,YAAW,CAAC,KAAK,QAAQ;AAC9C,YAAA,OAAO,SAAS,GAAG,GAAG;AAClB,gBAAA,iBAAiB,KAAK,GAAG;AAAA,QAAA;AAEjC,cAAM,KAAK,GAAG;AAAA,MAAA;AAAA,IAChB;AAEF,QAAI,mBAAK,UAAS;AACZ,UAAA,OAAO,QAAQ,UAAU;AACrB,cAAA;AAAA,MAAA;AAEG,iBAAA,KAAK,mBAAK,UAAS;AACrB,eAAA;AACH,YAAA,QAAQ,KAAK,CAAC,OAAO,SAAS,GAAG,KAAK,OAAO,MAAM,GAAG,GAAG;AAC3D;AAAA,QAAA;AAAA,MACF;AAEE,UAAA,OAAO,SAAS,GAAG,GAAG;AACxB,cAAM,GAAG,iBAAiB,KAAK,GAAG,CAAC;AAAA,MAAA;AAErC,UAAI,CAAC,mBAAK,YAAW,CAAC,KAAK,QAAQ;AACjC,cAAM,KAAK,GAAG;AAAA,MAAA;AAAA,IAChB;AAEF,QAAI,mBAAK,UAAS;AAChB,UAAI,MAAM;AACV,UAAI,MAAM;AACV,UAAI,MAAM;AACN,UAAA,mBAAK,SAAQ,QAAQ;AACnB,YAAA,mBAAK,SAAQ,WAAW,GAAG;AAC5B,WAAA,GAAG,IAAI,mBAAK;AAAA,QAAA,OACR;AACC,gBAAA,GAAG,KAAK,KAAK,mBAAK,QAAO,EAAE,KAAK,KAAK,CAAC;AAAA,QAAA;AAAA,MAC9C;AAEE,UAAA,mBAAK,SAAQ,QAAQ;AACnB,YAAA,mBAAK,SAAQ,WAAW,GAAG;AAC5B,WAAA,GAAG,IAAI,mBAAK;AAAA,QAAA,OACR;AACC,gBAAA,GAAG,KAAK,KAAK,mBAAK,QAAO,EAAE,KAAK,KAAK,CAAC;AAAA,QAAA;AAAA,MAC9C;AAEE,UAAA,OAAO,SAAS,GAAG,GAAG;AACxB,YAAI,KAAK;AACP,cAAI,KAAK;AACH,gBAAA,IAAI,SAAS,GAAG,GAAG;AACrB,oBAAM,KAAK,QAAQ,GAAG,MAAM,GAAG,OAAO,GAAG,MAAM;AAAA,gBAC7C,kBAAkB;AAAA,cAAA,CACnB;AAAA,YAAA,OACI;AACL,oBAAM,KAAK,QAAQ,GAAG,MAAM,GAAG,MAAM,GAAG,KAAK;AAAA,gBAC3C,kBAAkB;AAAA,cAAA,CACnB;AAAA,YAAA;AAAA,UACH,OACK;AACL,kBAAM,KAAK,QAAQ,GAAG,MAAM,GAAG,KAAK;AAAA,cAClC,kBAAkB;AAAA,YAAA,CACnB;AAAA,UAAA;AAAA,QAEM,WAAA,IAAI,SAAS,GAAG,GAAG;AAC5B,gBAAM,KAAK,QAAQ,GAAG,OAAO,GAAG,MAAM;AAAA,YACpC,kBAAkB;AAAA,UAAA,CACnB;AAAA,QAAA,OACI;AACL,gBAAM,KAAK,QAAQ,GAAG,MAAM,GAAG,KAAK;AAAA,YAClC,kBAAkB;AAAA,UAAA,CACnB;AAAA,QAAA;AAEH,cAAM,KAAK,IAAI,QAAQ,SAAS,EAAE,CAAC;AAAA,MAAA,OAC9B;AACL,YAAI,CAAC,MAAM,UAAU,QAAQ,QAAW;AACtC,gBAAM,KAAK,GAAG;AAAA,QAAA;AAEhB,YAAI,KAAK;AACP,cAAI,KAAK;AACH,gBAAA,IAAI,SAAS,GAAG,GAAG;AACrB,oBAAM,KAAK,QAAQ,GAAG,OAAO,GAAG,MAAM;AAAA,gBACpC,kBAAkB;AAAA,cAAA,CACnB;AAAA,YAAA,OACI;AACL,oBAAM,KAAK,QAAQ,GAAG,MAAM,GAAG,KAAK;AAAA,gBAClC,kBAAkB;AAAA,cAAA,CACnB;AAAA,YAAA;AAAA,UACH,OACK;AACC,kBAAA,KAAK,QAAQ,GAAG,KAAK;AAAA,cACzB,kBAAkB;AAAA,YAAA,CACnB;AAAA,UAAA;AAEH,cAAI,MAAM,QAAQ;AAChB,kBAAM,KAAK,KAAK,IAAI,QAAQ,SAAS,EAAE,CAAC;AAAA,UAAA,OACnC;AACL,kBAAM,KAAK,IAAI,QAAQ,SAAS,EAAE,CAAC;AAAA,UAAA;AAAA,QACrC,OACK;AACC,gBAAA,KAAK,QAAQ,GAAG,KAAK;AAAA,YACzB,kBAAkB;AAAA,UAAA,CACnB;AACD,cAAI,MAAM,QAAQ;AAChB,kBAAM,KAAK,KAAK,IAAI,QAAQ,SAAS,EAAE,CAAC;AAAA,UAAA,OACnC;AACL,kBAAM,KAAK,KAAK,KAAK,IAAI,QAAQ,SAAS,EAAE,CAAC;AAAA,UAAA;AAAA,QAC/C;AAAA,MACF;AAAA,IACF;AAEF,QAAI,mBAAK,UAAS;AACZ,UAAA,mBAAK,SAAQ,QAAQ;AACvB,YAAI,CAAC,MAAM,UAAU,QAAQ,QAAW;AACtC,gBAAM,KAAK,GAAG;AAAA,QAAA;AAEhB,cAAM,MAAM,KAAK,KAAK,mBAAK,QAAO,EAAE,KAAK,KAAK;AAC9C,YAAI,MAAM,QAAQ;AACV,gBAAA,KAAK,KAAK,GAAG,EAAE;AAAA,QAAA,OAChB;AACC,gBAAA,KAAK,GAAG,GAAG,EAAE;AAAA,QAAA;AAAA,MACrB;AAEE,UAAA,mBAAK,SAAQ,QAAQ;AACvB,cAAM,MAAM,KAAK,KAAK,mBAAK,QAAO,EAAE,KAAK,KAAK;AAC1C,YAAA,IAAI,SAAS,GAAG,GAAG;AACrB,cAAI,MAAM,QAAQ;AACV,kBAAA,KAAK,MAAM,GAAG,GAAG;AAAA,UAAA,OAClB;AACC,kBAAA,KAAK,QAAQ,GAAG,GAAG;AAAA,UAAA;AAAA,QAC3B,WACS,MAAM,QAAQ;AACjB,gBAAA,KAAK,KAAK,GAAG,EAAE;AAAA,QAAA,OAChB;AACC,gBAAA,KAAK,OAAO,GAAG,EAAE;AAAA,QAAA;AAAA,MACzB;AAAA,IACF;AAEF,QAAI,MAAM,QAAQ;AACT,aAAA,MAAM,KAAK,GAAG;AAAA,IAAA;AAEhB,WAAA;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOT,MAAc;AACZ,UAAM,QAAQ,CAAC;AACf,QAAI,mBAAK,UAAS;AAChB,UAAI,MAAM;AACC,iBAAA,KAAK,mBAAK,UAAS;AACrB,eAAA;AACH,YAAA,CAAC,OAAO,SAAS,GAAG,KAAK,OAAO,MAAM,GAAG,GAAG;AAC9C;AAAA,QAAA;AAAA,MACF;AAEF,YAAM,KAAK,GAAG;AAAA,IAAA;AAEhB,QAAI,mBAAK,UAAS;AAChB,UAAI,MAAuB;AAChB,iBAAA,KAAK,mBAAK,UAAS;AACrB,eAAA;AACP,YAAI,CAAC,OAAO,SAAS,GAAG,GAAG;AACzB;AAAA,QAAA;AAAA,MACF;AAEE,UAAA,OAAO,SAAS,GAAG,GAAG;AACxB,cAAM,GAAG,GAAG;AAAA,MAAA;AAEd,UAAI,MAAM,QAAQ;AACV,cAAA,KAAK,KAAK,GAAG,EAAE;AAAA,MAAA,OAChB;AACL,cAAM,KAAK,GAAG;AAAA,MAAA;AAAA,IAChB;AAEF,QAAI,mBAAK,UAAS;AAChB,UAAI,KAAK,KAAK;AACV,UAAA,mBAAK,SAAQ,QAAQ;AACvB,cAAM,KAAK,KAAK,mBAAK,QAAO,EAAE,KAAK,KAAK;AAAA,MAAA;AAEtC,UAAA,mBAAK,SAAQ,QAAQ;AACvB,cAAM,KAAK,KAAK,mBAAK,QAAO,EAAE,KAAK,KAAK;AAAA,MAAA;AAE1C,UAAI,KAAK;AACP,YAAI,KAAK;AACH,cAAA,IAAI,SAAS,GAAG,GAAG;AACrB,kBAAM,KAAK,QAAQ,GAAG,OAAO,GAAG,MAAM;AAAA,cACpC,kBAAkB;AAAA,YAAA,CACnB;AAAA,UAAA,OACI;AACL,kBAAM,KAAK,QAAQ,GAAG,MAAM,GAAG,KAAK;AAAA,cAClC,kBAAkB;AAAA,YAAA,CACnB;AAAA,UAAA;AAAA,QACH,OACK;AACC,gBAAA,KAAK,QAAQ,GAAG,KAAK;AAAA,YACzB,kBAAkB;AAAA,UAAA,CACnB;AAAA,QAAA;AAAA,MACH,OACK;AACC,cAAA,KAAK,cAAc,GAAG,MAAM;AAAA,UAChC,kBAAkB;AAAA,QAAA,CACnB;AAAA,MAAA;AAEH,UAAI,MAAM,QAAQ;AAChB,cAAM,KAAK,KAAK,IAAI,QAAQ,SAAS,EAAE,CAAC;AAAA,MAAA,OACnC;AACL,cAAM,KAAK,IAAI,QAAQ,SAAS,EAAE,CAAC;AAAA,MAAA;AAAA,IACrC;AAEF,QAAI,mBAAK,UAAS;AACZ,UAAA,mBAAK,SAAQ,QAAQ;AACvB,cAAM,MAAM,KAAK,KAAK,mBAAK,QAAO,EAC/B,IAAI,CAAQ,SAAA;AACP,cAAA;AACJ,cACE,aAAa,KAAK,IAAI,KACtB,CAAC,KAAK,WAAW,GAAG,KACpB,CAAC,KAAK,SAAS,GAAG,GAClB;AACA,kBAAM,IAAI,IAAI;AAAA,UAAA,OACT;AACC,kBAAA;AAAA,UAAA;AAED,iBAAA;AAAA,QAAA,CACR,EACA,KAAK,KAAK;AACb,YAAI,MAAM,QAAQ;AACZ,cAAA,mBAAK,SAAQ,SAAS,GAAG;AACrB,kBAAA,KAAK,MAAM,GAAG,GAAG;AAAA,UAAA,OAClB;AACC,kBAAA,KAAK,KAAK,GAAG,EAAE;AAAA,UAAA;AAAA,QACvB,OACK;AACC,gBAAA,KAAK,GAAG,GAAG,EAAE;AAAA,QAAA;AAAA,MACrB;AAEE,UAAA,mBAAK,SAAQ,QAAQ;AACvB,cAAM,MAAM,KAAK,KAAK,mBAAK,QAAO,EAC/B,IAAI,CAAQ,SAAA;AACP,cAAA;AACJ,cACE,aAAa,KAAK,IAAI,KACtB,CAAC,KAAK,WAAW,GAAG,KACpB,CAAC,KAAK,SAAS,GAAG,GAClB;AACA,kBAAM,IAAI,IAAI;AAAA,UAAA,OACT;AACC,kBAAA;AAAA,UAAA;AAED,iBAAA;AAAA,QAAA,CACR,EACA,KAAK,KAAK;AACb,YAAI,MAAM,QAAQ;AACZ,cAAA,mBAAK,SAAQ,SAAS,GAAG;AACrB,kBAAA,KAAK,MAAM,GAAG,GAAG;AAAA,UAAA,OAClB;AACC,kBAAA,KAAK,KAAK,GAAG,EAAE;AAAA,UAAA;AAAA,QAEd,WAAA,mBAAK,SAAQ,SAAS,GAAG;AAC5B,gBAAA,KAAK,SAAS,GAAG,GAAG;AAAA,QAAA,OACrB;AACC,gBAAA,KAAK,QAAQ,GAAG,EAAE;AAAA,QAAA;AAAA,MAC1B;AAAA,IACF;AAEF,QAAI,MAAM,QAAQ;AACT,aAAA,MAAM,KAAK,GAAG;AAAA,IAAA;AAEhB,WAAA;AAAA,EAAA;AAEX;AA7dE;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAmdK,MAAM,iBAAiB,CAC5B,SAA8B,IAC9B,WAAoB,UACT;AACP,MAAA,OAAO,SAAS,MAAM;AACxB,UAAM,IAAI,MAAM,2BAA2B,OAAO,MAAM,GAAG;AAAA,EAAA;AAEvD,QAAA,QAAQ,OAAO,MAAM;AACvB,MAAA,CAAC,SAAS,KAAK,KAAK,CAAC,MAAM,SAAS,GAAG,GAAG;AAC5C,UAAM,IAAI,MAAM,oBAAoB,KAAK,GAAG;AAAA,EAAA;AAExC,QAAA,MAAM,OAAO,IAAI;AACvB,MAAI,QAAQ,KAAK;AACf,UAAM,IAAI,MAAM,oBAAoB,GAAG,GAAG;AAAA,EAAA;AAExC,MAAA,OAAO,WAAW,GAAG;AACjB,UAAA,CAAC,KAAK,IAAI;AACZ,QAAA,CAAC,iBAAiB,KAAK,GAAG;AAC5B,YAAM,IAAI,MAAM,oBAAoB,KAAK,GAAG;AAAA,IAAA;AAE9C,WAAO,GAAG,KAAK,GAAG,KAAK,GAAG,GAAG;AAAA,EAAA;AAE/B,QAAM,eAAe,CAAC;AAChB,QAAA,MAAM,IAAI,WAAW;AAC3B,MAAI,WAAmB;AACvB,QAAM,IAAI,OAAO;AACjB,WAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AACpB,UAAA,QAAQ,OAAO,CAAC;AAClB,QAAA,CAAC,iBAAiB,KAAK,GAAG;AAC5B,YAAM,IAAI,MAAM,oBAAoB,KAAK,GAAG;AAAA,IAAA;AAE1C,QAAA,UAAU,OAAO,UAAU,KAAK;AACvB,iBAAA;AAAA,IACF,WAAA,UAAU,OAAO,UAAU,KAAK;AACnC,YAAA,cAAc,IAAI,SAAS;AACjC,UAAI,aAAa;AACF,qBAAA,KAAK,aAAa,KAAK;AAAA,MAAA;AAEtC,UAAI,MAAM;AACC,iBAAA;AAAA,IAAA,OACN;AACC,YAAA,WAAW,OAAO,KAAK;AACvB,YAAA,WAAW,GAAG,KAAK;AACzB,cAAQ,UAAU;AAAA,QAChB,KAAK,KAAK;AACJ,cAAA,OAAO,SAAS,QAAQ,GAAG;AAC7B,gBAAI,SAAS;AACT,gBAAA,OAAO,KAAK,IAAI,QAAQ;AAAA,UACnB,WAAA,aAAa,KAAK,QAAQ,GAAG;AACtC,kBAAM,CAAG,EAAA,GAAG,IAAI,SAAS,MAAM,YAAY;AAC3C,gBAAI,SAAS;AACb,gBAAI,OAAO,KAAM,UAAU,UAAW,OAAO,GAAG,CAAC;AAAA,UACxC,WAAA,aAAa,KAAK,QAAQ,GAAG;AACtC,gBAAI,SAAS;AACT,gBAAA,OAAO,KAAK,QAAQ;AAAA,UAAA,OACnB;AACL,gBAAI,SAAS;AACT,gBAAA,OAAO,KAAK,QAAQ;AAAA,UAAA;AAE1B;AAAA,QAAA;AAAA,QAEF,KAAK;AAAA,QACL,SAAS;AACH,cAAA,OAAO,SAAS,QAAQ,GAAG;AAC7B,gBAAI,SAAS;AACT,gBAAA,OAAO,KAAK,QAAQ;AAAA,UACf,WAAA,aAAa,KAAK,QAAQ,GAAG;AACtC,kBAAM,CAAG,EAAA,GAAG,IAAI,SAAS,MAAM,YAAY;AAC3C,gBAAI,SAAS;AACb,gBAAI,OAAO,KAAK,OAAO,GAAG,CAAC;AAAA,UAClB,WAAA,aAAa,KAAK,QAAQ,GAAG;AACtC,gBAAI,SAAS;AACT,gBAAA,OAAO,KAAK,QAAQ;AAAA,UAAA,OACnB;AACL,gBAAI,SAAS;AACT,gBAAA,OAAO,KAAK,QAAQ;AAAA,UAAA;AAAA,QAC1B;AAAA,MACF;AAAA,IACF;AAEE,QAAA,MAAM,IAAI,GAAG;AACT,YAAA,cAAc,IAAI,SAAS;AACjC,UAAI,aAAa;AACf,qBAAa,KAAK,WAAW;AAAA,MAAA;AAE/B,UAAI,MAAM;AACC,iBAAA;AAAA,IAAA;AAAA,EACb;AAEF,MAAI,gBAAgB;AAChB,MAAA,aAAa,aAAa,SAAS,GAAG,KAAK,aAAa,SAAS,GAAG,IAAI;AAC1E,UAAM,kBAAkB,CAAC;AACzB,QAAI,MAAM;AACC,eAAA;AACX,UAAMA,KAAI,aAAa;AACvB,aAAS,IAAI,GAAG,IAAIA,IAAG,KAAK;AACpB,YAAA,QAAQ,aAAa,CAAC;AACxB,UAAA,iBAAiB,KAAK,GAAG;AACvB,YAAA,UAAU,OAAO,UAAU,KAAK;AACvB,qBAAA;AAAA,QAAA,OACN;AACC,gBAAA,WAAW,OAAO,KAAK;AACvB,gBAAA,WAAW,GAAG,KAAK;AACzB,kBAAQ,UAAU;AAAA,YAChB,KAAK,KAAK;AACJ,kBAAA,OAAO,SAAS,QAAQ,GAAG;AAC7B,oBAAI,SAAS;AACT,oBAAA,OAAO,KAAK,KAAK,QAAQ;AAAA,cACpB,WAAA,aAAa,KAAK,QAAQ,GAAG;AACtC,sBAAM,CAAG,EAAA,GAAG,IAAI,SAAS,MAAM,YAAY;AAC3C,oBAAI,SAAS;AACb,oBAAI,OAAO,KAAK,KAAK,OAAO,GAAG,CAAC;AAAA,cACvB,WAAA,aAAa,KAAK,QAAQ,GAAG;AACtC,oBAAI,SAAS;AACT,oBAAA,OAAO,KAAK,QAAQ;AAAA,cAAA,OACnB;AACL,oBAAI,SAAS;AACT,oBAAA,OAAO,KAAK,QAAQ;AAAA,cAAA;AAE1B;AAAA,YAAA;AAAA,YAEF,KAAK;AAAA,YACL,SAAS;AACH,kBAAA,OAAO,SAAS,QAAQ,GAAG;AAC7B,oBAAI,SAAS;AACT,oBAAA,OAAO,KAAK,QAAQ;AAAA,cACf,WAAA,aAAa,KAAK,QAAQ,GAAG;AACtC,sBAAM,CAAG,EAAA,GAAG,IAAI,SAAS,MAAM,YAAY;AAC3C,oBAAI,SAAS;AACb,oBAAI,OAAO,KAAK,OAAO,GAAG,CAAC;AAAA,cAClB,WAAA,aAAa,KAAK,QAAQ,GAAG;AACtC,oBAAI,SAAS;AACT,oBAAA,OAAO,KAAK,QAAQ;AAAA,cAAA,OACnB;AACL,oBAAI,SAAS;AACT,oBAAA,OAAO,KAAK,QAAQ;AAAA,cAAA;AAAA,YAC1B;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAEE,UAAA,MAAMA,KAAI,GAAG;AACT,cAAA,cAAc,IAAI,IAAI;AAC5B,YAAI,aAAa;AACf,0BAAgB,KAAK,WAAW;AAAA,QAAA;AAElC,YAAI,MAAM;AACC,mBAAA;AAAA,MAAA;AAAA,IACb;AAEF,oBAAgB,gBAAgB,KAAK,GAAG,EAAE,QAAQ,UAAU,IAAI;AAAA,EAAA,OAC3D;AACL,oBAAgB,aAAa,KAAK,GAAG,EAAE,QAAQ,UAAU,IAAI;AAAA,EAAA;AAE/D,MACE,cAAc,WAAW,GAAG,KAC5B,cAAc,SAAS,GAAG,KAC1B,cAAc,YAAY,GAAG,MAAM,KACnC,cAAc,QAAQ,GAAG,MAAM,cAAc,SAAS,GACtD;AACA,oBAAgB,cAAc,QAAQ,OAAO,EAAE,EAAE,QAAQ,OAAO,EAAE;AAAA,EAAA;AAEpE,SAAO,GAAG,KAAK,GAAG,aAAa,GAAG,GAAG;AACvC;AAQO,MAAM,gBAAgB,CAAC,OAAe,MAAe,OAAe;AACnE,QAAA,EAAE,SAAS,GAAA,IAAO;AACpB,MAAA,SAAS,KAAK,GAAG;AACnB,QAAI,CAAC,iBAAiB,KAAK,KAAK,KAAK,WAAW,UAAU;AACjD,aAAA;AAAA,IAAA;AAED,YAAA,MAAM,YAAY,EAAE,KAAK;AAAA,EAAA,OAC5B;AACL,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EAAA;AAEjD,QAAM,WAAmB;AAAA,IACvB;AAAA,MACE,WAAW;AAAA,MACX,MAAM;AAAA,MACN;AAAA,IACF;AAAA,IACA;AAAA,EACF;AACM,QAAA,eAAe,SAAS,QAAQ;AACtC,MAAI,wBAAwB,WAAW;AACrC,WAAO,aAAa;AAAA,EAAA;AAEhB,QAAA,QAAkB,SAAS,EAAE,KAAK,OAAO,EAC5C,IAAI,CAAC,UAA4B;AAC1B,UAAA,CAAC,MAAMC,MAAK,IAAI;AACtB,QAAI,MAAM;AACN,QAAA,SAAS,WAAW,SAAS,SAAS;AAClCA,YAAAA;AAAAA,IAAA;AAED,WAAA;AAAA,EACR,CAAA,EACA,OAAO,CAAA,MAAK,CAAC;AACZ,MAAA,aAAa,MAAM,cAAc,CAAC,SAAiB,MAAM,KAAK,IAAI,CAAC;AACvE,SAAO,YAAY;AACjB,UAAM,WAAW,MAAM,UAAU,CAAC,MAAe,UAAkB;AAC1D,aAAA,SAAS,OAAO,QAAQ;AAAA,IAAA,CAChC;AACD,UAAM,eAAyB,MAAM,MAAM,YAAY,WAAW,CAAC;AAC/D,QAAA,kBAA0B,eAAe,YAAY;AACrD,QAAA,iBAAiB,KAAK,eAAe,GAAG;AAC1C,wBAAkB,KAAK,iBAAiB;AAAA,QACtC,kBAAkB;AAAA,MAAA,CACnB;AAAA,IAAA;AAEH,UAAM,OAAO,YAAY,WAAW,aAAa,GAAG,eAAe;AACnE,iBAAa,MAAM,cAAc,CAAC,SAAiB,MAAM,KAAK,IAAI,CAAC;AAAA,EAAA;AAE/D,QAAA,iBAAiB,eAAe,OAAO,IAAI;AACjD,WAAS,UAAU,cAAc;AAC1B,SAAA;AACT;AAQO,MAAM,mBAAmB,CAC9B,OACA,MAAe,OACS;AACxB,MAAI,CAAC,MAAM,QAAQ,KAAK,GAAG;AACzB,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EAAA;AAE3C,QAAA,CAAS,EAAA,EAAA,EAAA,EAAA,SAAS,CAAE,CAAA,IAAI;AACxB,QAAA,EAAE,MAAM,MAAA,IAAU;AAIxB,QAAM,EAAE,YAAY,CAAC,EAAA,IAAM;AAC3B,MAAI,SAAS,MAAM;AACV,WAAA,GAAG,KAAK,GAAG,IAAI;AAAA,EAAA;AAElB,QAAA,gBAAgB,OAAO,KAAK;AAClC,MAAI,QAAQ,OAAO,SAAS,aAAa,GAAG;AACtC,QAAA;AACJ,QAAI,OAAO,eAAe,KAAK,WAAW,IAAI,GAAG;AAC/C,mBAAa,UAAU,IAAI;AAAA,IAClB,WAAA,OAAO,UAAU,aAAa,YAAY;AACtC,mBAAA,UAAU,SAAS,IAAI;AAAA,IAAA;AAEtC,iBAAa,OAAO,UAAU;AAC1B,QAAA,OAAO,SAAS,UAAU,GAAG;AACxB,aAAA,GAAG,gBAAgB,UAAU;AAAA,IAAA;AAAA,EACtC;AAEF,SAAO,IAAI,WAAW;AACxB;AAQO,MAAM,cAAc,CACzB,QACA,MAAe,OACF;AACb,MAAI,CAAC,MAAM,QAAQ,MAAM,GAAG;AAC1B,UAAM,IAAI,UAAU,GAAG,MAAM,mBAAmB;AAAA,EAAA;AAE5C,QAAA,EAAE,SAAS,GAAA,IAAO;AAClB,QAAA,+BAAe,IAAI;AACzB,MAAI,OAAO;AACX,QAAM,MAAgB,CAAC;AACvB,SAAO,OAAO,QAAQ;AACd,UAAA,QAAQ,OAAO,MAAM;AAC3B,QAAI,CAAC,MAAM,QAAQ,KAAK,GAAG;AACzB,YAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,IAAA;AAEjD,UAAM,CAAC,OAAO,IAAI,QAAQ,EAAE,IAAI;AAChC,YAAQ,MAAM;AAAA,MACZ,KAAK,KAAK;AACR,YAAI,WAAW,YAAY,CAAC,SAAS,IAAI,IAAI,GAAG;AAC9C,cAAI,KAAK,KAAK;AAAA,QAAA,OACT;AACC,gBAAA,gBAAgB,iBAAiB,OAAO,GAAG;AAC7C,cAAA,SAAS,aAAa,GAAG;AAC3B,gBAAI,KAAK,aAAa;AAAA,UAAA,OACjB;AACL,gBAAI,KAAK,KAAK;AAAA,UAAA;AAAA,QAChB;AAEF;AAAA,MAAA;AAAA,MAEF,KAAK;AAAA,MACL,KAAK,YAAY;AACf,YAAI,KAAK,KAAK;AACd;AACI,YAAA,kBAAkB,KAAK,KAAK,GAAG;AACjC,mBAAS,IAAI,IAAI;AAAA,QAAA;AAEnB;AAAA,MAAA;AAAA,MAEF,KAAK,aAAa;AAChB,YAAI,IAAI,QAAQ;AACd,gBAAM,YAAY,IAAI,IAAI,SAAS,CAAC;AACpC,cAAI,cAAc,KAAK;AACjB,gBAAA,OAAO,IAAI,GAAG,KAAK;AAAA,UAAA,OAClB;AACL,gBAAI,KAAK,KAAK;AAAA,UAAA;AAAA,QAChB,OACK;AACL,cAAI,KAAK,KAAK;AAAA,QAAA;AAEZ,YAAA,SAAS,IAAI,IAAI,GAAG;AACtB,mBAAS,OAAO,IAAI;AAAA,QAAA;AAEtB;AACA;AAAA,MAAA;AAAA,MAEF,KAAK,SAAS;AACZ,YAAI,IAAI,QAAQ;AACd,gBAAM,YAAY,IAAI,IAAI,SAAS,CAAC;AAElC,cAAA,SAAS,SAAS,KAClB,CAAC,UAAU,SAAS,GAAG,KACvB,cAAc,KACd;AACA,gBAAI,KAAK,KAAK;AAAA,UAAA;AAAA,QAChB;AAEF;AAAA,MAAA;AAAA,MAEF,SAAS;AACH,YAAA,SAAS,WAAW,SAAS,KAAK;AACpC,cAAI,KAAK,KAAK;AAAA,QAAA;AAAA,MAChB;AAAA,IACF;AAAA,EACF;AAEK,SAAA;AACT;AAQO,MAAM,UAAU,CAAC,OAAe,MAAe,OAAe;AAC7D,QAAA,EAAE,SAAS,GAAA,IAAO;AACpB,MAAA,SAAS,KAAK,GAAG;AACf,QAAA,WAAW,KAAK,KAAK,GAAG;AAC1B,UAAI,WAAW,UAAU;AAChB,eAAA;AAAA,MAAA,OACF;AACCC,cAAAA,iBAAgB,WAAW,OAAO,GAAG;AACvC,YAAA,SAASA,cAAa,GAAG;AACpBA,iBAAAA;AAAAA,QAAA,OACF;AACE,iBAAA;AAAA,QAAA;AAAA,MACT;AAAA,IAEO,WAAA,CAAC,YAAY,KAAK,KAAK,GAAG;AAC5B,aAAA;AAAA,IAAA;AAED,YAAA,MAAM,YAAY,EAAE,KAAK;AAAA,EAAA,OAC5B;AACL,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EAAA;AAEjD,QAAM,WAAmB;AAAA,IACvB;AAAA,MACE,WAAW;AAAA,MACX,MAAM;AAAA,MACN;AAAA,IACF;AAAA,IACA;AAAA,EACF;AACM,QAAA,eAAe,SAAS,QAAQ;AACtC,MAAI,wBAAwB,WAAW;AACrC,WAAO,aAAa;AAAA,EAAA;AAEtB,QAAM,SAAS,SAAS,EAAE,KAAK,OAAO;AAChC,QAAA,SAAS,YAAY,QAAQ,GAAG;AACtC,MAAI,gBAAwB,KAAK,OAAO,KAAK,EAAE,GAAG;AAAA,IAChD,kBAAkB;AAAA,EAAA,CACnB;AACG,MAAA,iBAAiB,KAAK,KAAK,GAAG;AAC5B,QAAA,iBAAiB,KAAK,aAAa,GAAG;AACxC,YAAM,GAAG,KAAK,IAAI,IAAI,cAAc;AAAA,QAClC;AAAA,MACF;AACgB,sBAAA,GAAG,iBAAiB,OAAO,GAAG,GAAG,GAAG,CAAC,GAAG,IAAI;AAAA,IAAA;AAG9D,QACE,iBACA,CAAC,iBAAiB,KAAK,aAAa,KACpC,WAAW,UACX;AACA,sBAAgB,QAAQ,aAAa;AAAA,IAAA;AAAA,EACvC;AAEF,MAAI,WAAW,UAAU;AACnB,QAAA,aAAa,KAAK,aAAa,KAAK,CAAC,cAAc,SAAS,KAAK,GAAG;AACtD,sBAAA,cAAc,eAAe,GAAG;AAAA,IACvC,WAAA,gBAAgB,KAAK,aAAa,GAAG;AAC9C,YAAM,CAAG,EAAA,GAAG,IAAI,cAAc,MAAM,eAAe;AACnD,sBAAgB,QAAQ,iBAAiB,OAAO,GAAG,GAAG,GAAG,CAAC;AAAA,IAAA;AAAA,EAC5D;AAEF,WAAS,UAAU,aAAa;AACzB,SAAA;AACT;"}