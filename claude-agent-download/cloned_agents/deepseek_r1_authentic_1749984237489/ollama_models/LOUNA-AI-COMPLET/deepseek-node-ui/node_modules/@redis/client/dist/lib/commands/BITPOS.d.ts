import { RedisCommandArgument, RedisCommandArguments } from '.';
import { BitValue } from './generic-transformers';
export declare const FIRST_KEY_INDEX = 1;
export declare const IS_READ_ONLY = true;
export declare function transformArguments(key: RedisCommandArgument, bit: BitValue, start?: number, end?: number, mode?: 'BYTE' | 'BIT'): RedisCommandArguments;
export declare function transformReply(): number;
