"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.transformArguments = exports.FIRST_KEY_INDEX = void 0;
const generic_transformers_1 = require("./generic-transformers");
exports.FIRST_KEY_INDEX = 2;
function transformArguments(operation, destKey, key) {
    return (0, generic_transformers_1.pushVerdictArguments)(['BITOP', operation, destKey], key);
}
exports.transformArguments = transformArguments;
