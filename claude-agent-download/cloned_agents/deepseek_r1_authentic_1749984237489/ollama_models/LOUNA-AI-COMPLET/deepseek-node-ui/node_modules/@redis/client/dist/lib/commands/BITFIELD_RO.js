"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.transformArguments = exports.IS_READ_ONLY = exports.FIRST_KEY_INDEX = void 0;
exports.FIRST_KEY_INDEX = 1;
exports.IS_READ_ONLY = true;
function transformArguments(key, operations) {
    const args = ['BITFIELD_RO', key];
    for (const operation of operations) {
        args.push('GET', operation.encoding, operation.offset.toString());
    }
    return args;
}
exports.transformArguments = transformArguments;
