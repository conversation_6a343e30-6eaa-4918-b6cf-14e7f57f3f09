#!/usr/bin/env node

/**
 * SERVEUR POUR INTERFACE LOUNA-AI COMPLÈTE
 * Serveur spécialement adapté à l'interface avec QI 320, 42 mémoires, etc.
 */

const express = require('express');
const http = require('http');
const WebSocket = require('ws');
const path = require('path');
const fs = require('fs');

// Import des modules LOUNA-AI
const SystemeScanIntelligent = require('./systeme-scan-intelligent.js');
const GestionnaireApplicationsIntelligent = require('./gestionnaire-applications-intelligent.js');
const { MemoireThermiqueReelle } = require('./VERSIONS-NON-VALIDEES/memoires-non-validees/memoire-thermique-reelle.js');
const { RechercheGoogleSecurisee } = require('./recherche-google-securisee.js');
const { MoteurRaisonnementReel } = require('./moteur-raisonnement-reel.js');
// Modules avancés (avec gestion d'erreur)
let SystemeCognitifAvance, AutoEvolution, SystemeAutoFormationReel;
try {
    SystemeCognitifAvance = require('./systeme-cognitif-avance.js');
    console.log('✅ Système cognitif avancé chargé');
} catch (e) {
    console.log('⚠️ Système cognitif avancé non disponible');
    SystemeCognitifAvance = null;
}

try {
    AutoEvolution = require('./auto-evolution.js');
    console.log('✅ Auto-évolution chargée');
} catch (e) {
    console.log('⚠️ Auto-évolution non disponible');
    AutoEvolution = null;
}

try {
    SystemeAutoFormationReel = require('./systeme-auto-formation-reel.js');
    console.log('✅ Auto-formation chargée');
} catch (e) {
    console.log('⚠️ Auto-formation non disponible');
    SystemeAutoFormationReel = null;
}
const axios = require('axios');

class ServeurInterfaceComplete {
    constructor() {
        console.log('🚀 SERVEUR INTERFACE LOUNA-AI COMPLÈTE');
        console.log('=====================================');
        
        this.app = express();
        this.server = http.createServer(this.app);
        this.wss = new WebSocket.Server({ server: this.server });
        this.port = 3000;
        
        // Modules LOUNA-AI
        this.scanneur = new SystemeScanIntelligent();
        this.gestionnaire = new GestionnaireApplicationsIntelligent();
        
        // État de LOUNA-AI (sera initialisé dynamiquement)
        this.etat = {
            qi_actuel: 320, // Base, sera calculé dynamiquement
            memoires: 0,    // Sera calculé depuis la mémoire thermique
            temperature: 0, // Sera calculé depuis la mémoire thermique
            zone_active: 1, // Sera calculée dynamiquement
            connexions_actives: 0,
            derniere_activite: Date.now(),
            applications_detectees: 0,
            systeme_pret: false
        };
        
        this.clients = new Set();
        this.memoireThermique = null;
        this.rechercheGoogle = null;
        this.moteurRaisonnement = null;
        this.systemeCognitif = null;
        this.autoEvolution = null;
        this.autoFormation = null;
        this.initialiser();
    }

    async initialiser() {
        try {
            // Configuration Express
            this.app.use(express.json());
            this.app.use(express.static('.'));
            
            // Routes API
            this.configurerRoutes();
            
            // Configuration WebSocket
            this.configurerWebSocket();
            
            // Initialiser les modules
            await this.initialiserModules();
            
            // Démarrer le serveur
            this.server.listen(this.port, () => {
                console.log(`✅ Serveur démarré sur http://localhost:${this.port}`);
                console.log(`🌐 Interface: http://localhost:${this.port}/interface-louna-complete.html`);
                console.log(`📊 État: QI ${this.etat.qi_actuel}, ${this.etat.memoires} mémoires, ${this.etat.temperature}°C`);
            });
            
            // Mise à jour périodique
            this.demarrerMiseAJour();
            
        } catch (error) {
            console.error('❌ Erreur initialisation:', error);
        }
    }

    configurerRoutes() {
        // Route principale
        this.app.get('/', (req, res) => {
            res.sendFile(path.join(__dirname, 'interface-louna-complete.html'));
        });

        // API Status
        this.app.get('/api/status', (req, res) => {
            res.json({
                success: true,
                etat: this.etat,
                timestamp: Date.now()
            });
        });

        // API Stats
        this.app.get('/api/stats', (req, res) => {
            // Mettre à jour l'état avant de répondre
            this.mettreAJourEtat();

            let statsDetaillees = {};
            if (this.memoireThermique) {
                statsDetaillees.memoire_thermique = this.memoireThermique.getStatistiquesReelles();
            }

            // Ajouter statistiques auto-évolution
            if (this.autoEvolution && typeof this.autoEvolution.obtenirMetriques === 'function') {
                statsDetaillees.auto_evolution = this.autoEvolution.obtenirMetriques();
            } else if (this.autoEvolution) {
                statsDetaillees.auto_evolution = { status: 'actif', metriques: this.autoEvolution.metriques_evolution || {} };
            }

            // Ajouter statistiques auto-formation
            if (this.autoFormation && typeof this.autoFormation.obtenirStatistiques === 'function') {
                statsDetaillees.auto_formation = this.autoFormation.obtenirStatistiques();
            }

            // Ajouter statistiques système cognitif
            if (this.systemeCognitif && typeof this.systemeCognitif.obtenirStatistiques === 'function') {
                statsDetaillees.systeme_cognitif = this.systemeCognitif.obtenirStatistiques();
            }

            res.json({
                success: true,
                coefficient_intellectuel: this.etat.qi_actuel,
                qi_actuel: this.etat.qi_actuel,
                memoires: this.etat.memoires,
                temperature: this.etat.temperature,
                zone_active: this.etat.zone_active,
                applications_detectees: this.etat.applications_detectees,
                connexions_actives: this.etat.connexions_actives,
                systeme_pret: this.etat.systeme_pret,
                derniere_activite: this.etat.derniere_activite,
                stats: statsDetaillees,
                etat: this.etat
            });
        });

        // API Chat
        this.app.post('/api/chat', async (req, res) => {
            try {
                const { message } = req.body;
                const reponse = await this.traiterMessage(message);

                // Mettre à jour l'état après traitement
                this.mettreAJourEtat();

                res.json({
                    success: true,
                    reponse: reponse,
                    etat: this.etat
                });
            } catch (error) {
                res.status(500).json({
                    success: false,
                    error: error.message
                });
            }
        });

        // API Scan Applications
        this.app.get('/api/scan-apps', async (req, res) => {
            try {
                const resultat = await this.scanneur.scannerApplications();
                this.etat.applications_detectees = resultat.total || 0;
                res.json(resultat);
            } catch (error) {
                res.status(500).json({
                    success: false,
                    error: error.message
                });
            }
        });

        // API Ouvrir Application
        this.app.post('/api/open-app', async (req, res) => {
            try {
                const { nom_app } = req.body;
                const resultat = await this.gestionnaire.ouvrirApplication(nom_app);
                res.json(resultat);
            } catch (error) {
                res.status(500).json({
                    success: false,
                    error: error.message
                });
            }
        });
    }

    configurerWebSocket() {
        this.wss.on('connection', (ws) => {
            console.log('🔗 Nouvelle connexion WebSocket');
            this.clients.add(ws);
            this.etat.connexions_actives = this.clients.size;

            // Envoyer l'état initial
            ws.send(JSON.stringify({
                type: 'init',
                etat: this.etat
            }));

            ws.on('message', async (data) => {
                try {
                    const message = JSON.parse(data);
                    await this.traiterMessageWebSocket(ws, message);
                } catch (error) {
                    console.error('❌ Erreur message WebSocket:', error);
                }
            });

            ws.on('close', () => {
                console.log('❌ Connexion WebSocket fermée');
                this.clients.delete(ws);
                this.etat.connexions_actives = this.clients.size;
            });
        });
    }

    async initialiserModules() {
        console.log('🔄 Initialisation des modules...');

        try {
            // Initialiser la mémoire thermique
            this.memoireThermique = new MemoireThermiqueReelle();
            console.log('✅ Mémoire thermique initialisée');

            // Initialiser la recherche Google sécurisée
            this.rechercheGoogle = new RechercheGoogleSecurisee();
            console.log('✅ Recherche Google sécurisée initialisée');

            // Initialiser le moteur de raisonnement réel
            this.moteurRaisonnement = new MoteurRaisonnementReel();
            console.log('✅ Moteur de raisonnement réel initialisé');

            // Initialiser le système cognitif avancé
            if (SystemeCognitifAvance) {
                this.systemeCognitif = new SystemeCognitifAvance();
                console.log('✅ Système cognitif avancé initialisé');
            }

            // Initialiser l'auto-évolution
            if (AutoEvolution) {
                this.autoEvolution = new AutoEvolution();
                console.log('✅ Auto-évolution initialisée');
            }

            // Initialiser l'auto-formation
            if (SystemeAutoFormationReel) {
                this.autoFormation = new SystemeAutoFormationReel();
                console.log('✅ Auto-formation initialisée');
            }

            // Scanner les applications
            const resultatScan = await this.scanneur.scannerApplications();
            if (resultatScan.success) {
                this.etat.applications_detectees = resultatScan.total;
                console.log(`✅ ${resultatScan.total} applications détectées`);
            }

            // Scanner le système
            const resultatSysteme = await this.scanneur.scannerSystemeComplet();
            if (resultatSysteme.success) {
                console.log('✅ Système scanné');
            }

            this.etat.systeme_pret = true;

            // INITIALISER L'ÉTAT DYNAMIQUE
            this.mettreAJourEtat();
            console.log('✅ Modules initialisés');

        } catch (error) {
            console.error('❌ Erreur initialisation modules:', error);
        }
    }

    async traiterMessage(message) {
        this.etat.derniere_activite = Date.now();

        try {
            // 0. FILTRAGE COGNITIF AVANCÉ
            if (this.systemeCognitif) {
                const validationCognitive = this.systemeCognitif.filtrerQuestion(message);
                if (!validationCognitive.valide) {
                    console.log(`🚫 Question rejetée: ${validationCognitive.raison}`);
                    return `🤔 Je ne peux pas traiter cette question car ${validationCognitive.raison}. Pouvez-vous reformuler ?`;
                }
                console.log(`✅ Question validée (pertinence: ${validationCognitive.pertinence}, confiance: ${validationCognitive.confiance})`);
            }

            // Traitement intelligent du message
            if (message.toLowerCase().includes('scan') && message.toLowerCase().includes('app')) {
                const resultat = await this.scanneur.scannerApplications();
                this.etat.applications_detectees = resultat.total || 0;
                return `🔍 Scan terminé: ${resultat.total} applications détectées`;
            }

            if (message.toLowerCase().includes('ouvre') || message.toLowerCase().includes('lance')) {
                const resultat = await this.gestionnaire.traiterDemande(message);
                return resultat.message || 'Application traitée';
            }

            if (message.toLowerCase().includes('stats') || message.toLowerCase().includes('état')) {
                return `📊 État LOUNA-AI: QI ${this.etat.qi_actuel}, ${this.etat.memoires} mémoires, ${this.etat.temperature}°C, Zone ${this.etat.zone_active}`;
            }

            // 1. RECHERCHER EN MÉMOIRE THERMIQUE
            if (this.memoireThermique) {
                const resultatsMemoire = this.memoireThermique.rechercher(message);
                if (resultatsMemoire && resultatsMemoire.length > 0) {
                    const meilleurResultat = resultatsMemoire[0];
                    console.log(`🧠 Réponse depuis mémoire thermique: ${meilleurResultat.contenu.substring(0, 50)}...`);

                    // Vérifier la fraîcheur de l'information
                    if (meilleurResultat.pertinence > 0.8) {
                        return `🧠 ${meilleurResultat.contenu}`;
                    } else {
                        console.log('⚠️ Information en mémoire peu pertinente, recherche mise à jour...');

                        // Essayer de mettre à jour l'information
                        const infoMiseAJour = await this.searchInternetForUpdate(message);
                        if (infoMiseAJour) {
                            // Stocker la nouvelle information
                            this.memoireThermique.stocker(infoMiseAJour, 'Internet', 0.8);
                            return infoMiseAJour;
                        } else {
                            return `🧠 ${meilleurResultat.contenu} (information à vérifier)`;
                        }
                    }
                }
            }

            // 2. RAISONNEMENT INTERNE (PRIORITÉ)
            console.log('🧠 Tentative de raisonnement interne...');
            if (this.moteurRaisonnement) {
                const resultatRaisonnement = this.moteurRaisonnement.penser(message);
                if (resultatRaisonnement && resultatRaisonnement.reponse && resultatRaisonnement.reponse !== null) {
                    const reponseStr = String(resultatRaisonnement.reponse);
                    console.log(`🧠 Réponse par raisonnement: ${reponseStr.substring(0, 50)}...`);
                    console.log(`🧠 Source: ${resultatRaisonnement.source}`);

                    // Stocker en mémoire thermique avec haute priorité
                    if (this.memoireThermique) {
                        this.memoireThermique.stocker(reponseStr, 'Raisonnement', 0.9);
                    }

                    // Déclencher auto-formation si disponible
                    if (this.autoFormation && typeof this.autoFormation.analyserInteraction === 'function') {
                        this.autoFormation.analyserInteraction(message, reponseStr, 'raisonnement');
                    }

                    return `🧠 ${reponseStr}`;
                } else {
                    console.log('🧠 Aucune réponse par raisonnement interne');
                }
            }

            // 3. RECHERCHER SUR INTERNET
            console.log('🌐 Aucune information en mémoire, recherche Internet...');
            const infoInternet = await this.searchInternetForUpdate(message);
            if (infoInternet) {
                // Stocker en mémoire thermique
                if (this.memoireThermique) {
                    this.memoireThermique.stocker(infoInternet, 'Internet', 0.7);
                }
                return infoInternet;
            }

            // 4. FALLBACK VERS OLLAMA
            console.log('🤖 Fallback vers agent Ollama...');
            const reponseOllama = await this.queryOllama(message);
            if (reponseOllama) {
                // Stocker en mémoire thermique
                if (this.memoireThermique) {
                    this.memoireThermique.stocker(reponseOllama, 'Ollama', 0.6);
                }
                return reponseOllama;
            }

            // 5. FALLBACK FINAL
            return `🧠 LOUNA-AI: J'ai bien reçu votre message "${message}". Mon QI actuel est de ${this.etat.qi_actuel} et je fonctionne à ${this.etat.temperature}°C.`;

        } catch (error) {
            console.error('❌ Erreur traitement message:', error);
            return '❌ Erreur lors du traitement de votre message.';
        }
    }

    // CALCUL DYNAMIQUE DE LA ZONE ACTIVE
    calculerZoneActive() {
        if (!this.memoireThermique) return 1;

        try {
            const stats = this.memoireThermique.getStatistiquesReelles();
            if (!stats || !stats.zonesDistribution) return 1;

            // Trouver la zone avec le plus de mémoires actives
            let zoneMaxMemoires = 1;
            let maxMemoires = 0;

            for (let i = 0; i < stats.zonesDistribution.length; i++) {
                if (stats.zonesDistribution[i] > maxMemoires) {
                    maxMemoires = stats.zonesDistribution[i];
                    zoneMaxMemoires = i + 1;
                }
            }

            // Si pas de mémoires, utiliser la température moyenne pour déterminer la zone
            if (maxMemoires === 0 && stats.averageTemperature) {
                const tempMoyenne = stats.averageTemperature;
                const zones = [65, 55, 45, 35, 30, 25]; // Seuils des zones

                for (let i = 0; i < zones.length; i++) {
                    if (tempMoyenne >= zones[i]) {
                        return i + 1;
                    }
                }
                return 6; // Zone la plus froide
            }

            return zoneMaxMemoires;
        } catch (error) {
            console.log('⚠️ Erreur calcul zone active:', error.message);
            return 1;
        }
    }

    // MISE À JOUR DE L'ÉTAT DYNAMIQUE
    mettreAJourEtat() {
        if (this.memoireThermique) {
            const stats = this.memoireThermique.getStatistiquesReelles();
            if (stats) {
                // FORCER la mise à jour des valeurs
                this.etat.memoires = stats.totalEntries;
                this.etat.temperature = Math.round(stats.averageTemperature * 100) / 100;
                this.etat.zone_active = this.calculerZoneActive();

                // Calculer le QI dynamiquement selon les mémoires
                const qiBase = 320;
                const bonusMemoires = Math.floor(stats.totalEntries / 5) * 2; // +2 QI par 5 mémoires
                const bonusTemperature = Math.floor((stats.averageTemperature - 50) / 5) * 3; // +3 QI par 5°C au-dessus de 50°C
                this.etat.qi_actuel = qiBase + bonusMemoires + bonusTemperature;

                console.log(`📊 État mis à jour: QI ${this.etat.qi_actuel}, ${this.etat.memoires} mémoires, ${this.etat.temperature}°C, Zone ${this.etat.zone_active}`);

                // Déclencher auto-évolution si disponible
                if (this.autoEvolution && typeof this.autoEvolution.analyserEvolution === 'function') {
                    this.autoEvolution.analyserEvolution(this.etat);
                } else if (this.autoEvolution && typeof this.autoEvolution.executerCycleEvolution === 'function') {
                    this.autoEvolution.executerCycleEvolution();
                }
            }
        }
        this.etat.derniere_activite = Date.now();
    }

    async searchInternetForUpdate(query) {
        try {
            console.log(`🔍 Recherche Internet sécurisée pour: "${query}"`);

            // RÉPONSES SIMPLES DIRECTES
            const simpleAnswer = this.getSimpleAnswer(query);
            if (simpleAnswer) {
                console.log(`✅ Réponse simple trouvée: ${simpleAnswer}`);
                return simpleAnswer;
            }

            // RECHERCHE GOOGLE SÉCURISÉE
            if (this.rechercheGoogle) {
                console.log('🔍 Utilisation recherche Google sécurisée...');
                const resultatsGoogle = await this.rechercheGoogle.rechercherSecurise(query, 3);

                if (resultatsGoogle && resultatsGoogle.length > 0) {
                    const meilleurResultat = resultatsGoogle[0];
                    const reponse = `${meilleurResultat.description} (Source: ${meilleurResultat.source} - Sécurité: ${meilleurResultat.scoreSecurite}/100)`;

                    console.log(`✅ Résultat Google sécurisé trouvé: ${meilleurResultat.titre}`);
                    return reponse;
                }
            }

            return null;
        } catch (error) {
            console.log(`❌ Erreur recherche Internet: ${error.message}`);
            return null;
        }
    }

    getSimpleAnswer(query) {
        const lowerQuery = query.toLowerCase();

        // Mathématiques simples
        const mathMatch = lowerQuery.match(/(\d+)\s*[x*×]\s*(\d+)/);
        if (mathMatch) {
            const result = parseInt(mathMatch[1]) * parseInt(mathMatch[2]);
            return `${mathMatch[1]} × ${mathMatch[2]} = ${result}`;
        }

        const addMatch = lowerQuery.match(/(\d+)\s*\+\s*(\d+)/);
        if (addMatch) {
            const result = parseInt(addMatch[1]) + parseInt(addMatch[2]);
            return `${addMatch[1]} + ${addMatch[2]} = ${result}`;
        }

        // Capitales connues
        if (lowerQuery.includes('capitale')) {
            if (lowerQuery.includes('france')) return 'La capitale de la France est Paris.';
            if (lowerQuery.includes('italie')) return 'La capitale de l\'Italie est Rome.';
            if (lowerQuery.includes('espagne')) return 'La capitale de l\'Espagne est Madrid.';
            if (lowerQuery.includes('allemagne')) return 'La capitale de l\'Allemagne est Berlin.';
            if (lowerQuery.includes('japon')) return 'La capitale du Japon est Tokyo.';
            if (lowerQuery.includes('brésil') || lowerQuery.includes('bresil')) return 'La capitale du Brésil est Brasília.';
            if (lowerQuery.includes('maroc')) return 'La capitale du Maroc est Rabat.';
            if (lowerQuery.includes('guadeloupe')) return 'La préfecture de la Guadeloupe est Basse-Terre.';
        }

        // Informations sur LOUNA
        if (lowerQuery.includes('louna') || lowerQuery.includes('qui es-tu')) {
            return `Je suis LOUNA-AI, créée par Jean-Luc Passave à Sainte-Anne, Guadeloupe. Mon QI actuel est de ${this.etat.qi_actuel} et j'évolue constamment grâce à ma mémoire thermique et mes accélérateurs.`;
        }

        return null;
    }

    async queryOllama(message) {
        try {
            const response = await axios.post('http://localhost:11434/api/generate', {
                model: 'llama3.2:1b',
                prompt: message,
                stream: false
            }, {
                timeout: 30000
            });

            if (response.data && response.data.response) {
                return response.data.response;
            }
            return null;
        } catch (error) {
            console.log('❌ Ollama non disponible:', error.message);
            return null;
        }
    }

    async traiterMessageWebSocket(ws, message) {
        switch (message.type) {
            case 'chat':
                const reponse = await this.traiterMessage(message.content);
                ws.send(JSON.stringify({
                    type: 'chat_response',
                    reponse: reponse,
                    etat: this.etat
                }));
                break;

            case 'get_stats':
                ws.send(JSON.stringify({
                    type: 'stats',
                    etat: this.etat
                }));
                break;

            case 'scan_apps':
                const resultat = await this.scanneur.scannerApplications();
                this.etat.applications_detectees = resultat.total || 0;
                ws.send(JSON.stringify({
                    type: 'scan_result',
                    resultat: resultat,
                    etat: this.etat
                }));
                break;
        }
    }

    demarrerMiseAJour() {
        setInterval(() => {
            // MISE À JOUR RÉELLE (pas de simulation)
            this.mettreAJourEtat();

            // Diffuser les mises à jour réelles
            this.diffuserMiseAJour();
        }, 5000);
    }

    diffuserMiseAJour() {
        const message = JSON.stringify({
            type: 'update',
            etat: this.etat,
            timestamp: Date.now()
        });

        this.clients.forEach(client => {
            if (client.readyState === WebSocket.OPEN) {
                client.send(message);
            }
        });
    }
}

// Démarrage du serveur
if (require.main === module) {
    new ServeurInterfaceComplete();
}

module.exports = ServeurInterfaceComplete;
