#!/bin/bash
echo "🔄 RESTAURATION CONFIGURATION FINALE LOUNA-AI"
echo "============================================="

# Copier tous les fichiers vers le répertoire parent
cp -v *.js ../
cp -v *.html ../
cp -v *.md ../
cp -v package.json ../ 2>/dev/null || true

# Restaurer mémoire thermique
mkdir -p ../VERSIONS-NON-VALIDEES/memoires-non-validees/
cp -v VERSIONS-NON-VALIDEES/memoires-non-validees/*.js ../VERSIONS-NON-VALIDEES/memoires-non-validees/

echo "✅ Configuration finale restaurée"
echo "🚀 Lancer: node serveur-interface-complete.js"
