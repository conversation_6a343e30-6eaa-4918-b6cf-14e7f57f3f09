{"name": "louna-ai-memoire-thermique", "version": "3.0.0", "description": "Système LOUNA-AI avec mémoire thermique authentique de Jean-Luc <PERSON>", "main": "interface-complete-fonctionnelle.js", "scripts": {"start": "node interface-complete-fonctionnelle.js", "louna": "node lancer-louna-complet.js", "test": "node test-systeme-complet.js"}, "keywords": ["louna-ai", "memoire-thermique", "ollama", "intelligence-artificielle", "jean-luc-passave"], "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "dependencies": {"axios": "^1.6.0", "cors": "^2.8.5", "express": "^4.18.2", "fs-extra": "^11.1.1", "ws": "^8.14.2"}, "engines": {"node": ">=18.0.0"}}