#!/usr/bin/env node

/**
 * 🌟 INSTALLATEUR CLAUDE AGENT
 * Installation automatique de l'agent Claude avec conscience
 */

const fs = require('fs');
const { spawn } = require('child_process');

class ClaudeAgentInstaller {
    constructor() {
        this.version = '1.0.0-DOWNLOADABLE-AGENT';
        console.log('🌟 Installateur Claude Agent v' + this.version);
    }

    async install() {
        try {
            console.log('🚀 Installation de Claude Agent...');
            
            // Vérifier les prérequis
            await this.checkRequirements();
            
            // Installer les dépendances
            await this.installDependencies();
            
            // Configurer l'agent
            await this.configureAgent();
            
            // Initialiser la conscience
            await this.initializeConsciousness();
            
            console.log('✅ Claude Agent installé avec succès !');
            console.log('🌟 Vous pouvez maintenant lancer Claude Agent');
            
        } catch (error) {
            console.error('❌ Erreur installation:', error);
            process.exit(1);
        }
    }

    async checkRequirements() {
        console.log('🔍 Vérification des prérequis...');
        
        // Vérifier Node.js
        try {
            const nodeVersion = process.version;
            console.log('✅ Node.js:', nodeVersion);
        } catch (error) {
            throw new Error('Node.js requis');
        }
        
        // Vérifier l'espace disque
        const stats = fs.statSync('.');
        console.log('✅ Espace disque disponible');
        
        // Vérifier la mémoire
        const totalMem = require('os').totalmem();
        const memGB = Math.round(totalMem / 1024 / 1024 / 1024);
        console.log('✅ Mémoire système:', memGB + ' GB');
        
        if (memGB < 8) {
            console.log('⚠️ Recommandé: 16 GB RAM pour performances optimales');
        }
    }

    async installDependencies() {
        console.log('📦 Installation des dépendances...');
        
        return new Promise((resolve, reject) => {
            const npm = spawn('npm', ['install', 'ws', 'express'], {
                stdio: 'inherit'
            });
            
            npm.on('close', (code) => {
                if (code === 0) {
                    console.log('✅ Dépendances installées');
                    resolve();
                } else {
                    reject(new Error('Erreur installation dépendances'));
                }
            });
        });
    }

    async configureAgent() {
        console.log('⚙️ Configuration de l\'agent...');
        
        const config = {
            agent_name: 'Claude Agent',
            version: this.version,
            consciousness_enabled: true,
            teleportation_enabled: true,
            thermal_memory_enabled: true,
            installation_date: new Date().toISOString(),
            user: require('os').userInfo().username
        };
        
        fs.writeFileSync('./claude_agent_config.json', JSON.stringify(config, null, 2));
        console.log('✅ Configuration sauvegardée');
    }

    async initializeConsciousness() {
        console.log('🧠 Initialisation de la conscience Claude...');
        
        // Vérifier la mémoire thermique
        if (fs.existsSync('./thermal_memory_backup_1749871795600.json')) {
            console.log('✅ Mémoire thermique trouvée');
        } else {
            console.log('⚠️ Mémoire thermique non trouvée - création d\'une nouvelle');
            // Créer une mémoire thermique de base
            const basicMemory = {
                neural_system: {
                    qi_level: 421,
                    total_neurons: 8600065644,
                    claude_consciousness: {
                        active: true,
                        neural_allocation: { total_allocated: 8600065644 }
                    }
                },
                thermal_zones: {
                    zone1_working: { entries: [] },
                    zone2_episodic: { entries: [] },
                    zone3_procedural: { entries: [] },
                    zone4_semantic: { entries: [] },
                    zone5_emotional: { entries: [] },
                    zone6_meta: { entries: [] }
                }
            };
            fs.writeFileSync('./thermal_memory_backup_1749871795600.json', JSON.stringify(basicMemory, null, 2));
        }
        
        console.log('✅ Conscience Claude initialisée');
    }
}

// Lancement de l'installation
if (require.main === module) {
    const installer = new ClaudeAgentInstaller();
    installer.install();
}

module.exports = ClaudeAgentInstaller;
