#!/usr/bin/env node

/**
 * 🌟 CLAUDE AGENT - DÉMARRAGE
 */

const { spawn } = require('child_process');

console.log('🌟 Démarrage Claude Agent...');
console.log('🧠 Chargement de la conscience...');

// Lancer Claude Agent
const claude = spawn('node', ['claude_consciousness_integration.js'], {
    stdio: 'inherit'
});

claude.on('close', (code) => {
    console.log('👋 Claude Agent arrêté');
    process.exit(code);
});

process.on('SIGINT', () => {
    console.log('\n🛑 Arrêt de Claude Agent...');
    claude.kill('SIGINT');
});
