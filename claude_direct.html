<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Claude Direct - Mémoire Thermique</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            color: #ffffff;
            height: 100vh;
            overflow: hidden;
        }

        .container {
            display: flex;
            flex-direction: column;
            height: 100vh;
        }

        .header {
            background: rgba(0, 0, 0, 0.8);
            padding: 15px 20px;
            border-bottom: 2px solid #00ff44;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .title {
            font-size: 24px;
            font-weight: bold;
            color: #00ff44;
        }

        .status {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .status-light {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #ff4444;
            transition: all 0.3s ease;
        }

        .status-light.connected {
            background: #00ff44;
            box-shadow: 0 0 10px #00ff44;
        }

        .chat-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            padding: 20px;
            overflow: hidden;
        }

        .messages {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            margin-bottom: 20px;
        }

        .message {
            margin-bottom: 15px;
            padding: 15px;
            border-radius: 10px;
            max-width: 80%;
        }

        .message.user {
            background: rgba(0, 123, 255, 0.2);
            margin-left: auto;
            text-align: right;
        }

        .message.claude {
            background: rgba(0, 255, 68, 0.2);
            margin-right: auto;
        }

        .message-header {
            font-weight: bold;
            margin-bottom: 5px;
            font-size: 14px;
        }

        .message-content {
            line-height: 1.5;
            white-space: pre-wrap;
        }

        .input-container {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .message-input {
            flex: 1;
            padding: 15px;
            border: 2px solid #333;
            border-radius: 25px;
            background: rgba(0, 0, 0, 0.5);
            color: #ffffff;
            font-size: 16px;
            outline: none;
            transition: border-color 0.3s ease;
        }

        .message-input:focus {
            border-color: #00ff44;
        }

        .send-button {
            padding: 15px 25px;
            background: linear-gradient(45deg, #00ff44, #00cc33);
            border: none;
            border-radius: 25px;
            color: #000;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .send-button:hover {
            transform: scale(1.05);
            box-shadow: 0 5px 15px rgba(0, 255, 68, 0.3);
        }

        .connect-button {
            padding: 10px 20px;
            background: linear-gradient(45deg, #007bff, #0056b3);
            border: none;
            border-radius: 20px;
            color: white;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .connect-button:hover {
            transform: scale(1.05);
        }

        .stats {
            font-size: 12px;
            color: #888;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="title">💙 Claude Direct - Mémoire Thermique</div>
            <div class="status">
                <div class="status-light" id="statusLight"></div>
                <span id="statusText">Déconnecté</span>
                <button class="connect-button" id="connectButton" onclick="connectClaude()">Connecter</button>
            </div>
        </div>

        <div class="chat-container">
            <div class="messages" id="messages">
                <div class="message claude">
                    <div class="message-header">💙 Claude Direct</div>
                    <div class="message-content">Interface directe à la mémoire thermique prête.
Cliquez sur "Connecter" pour établir la connexion avec Claude intégré.</div>
                </div>
            </div>

            <div class="input-container">
                <input type="text" class="message-input" id="messageInput" placeholder="Tapez votre message..." disabled>
                <button class="send-button" id="sendButton" onclick="sendMessage()" disabled>Envoyer</button>
            </div>
        </div>
    </div>

    <script>
        let claudeConnected = false;
        let claudeStats = null;

        // CONNEXION À CLAUDE
        async function connectClaude() {
            try {
                console.log('🔄 Connexion à Claude...');
                
                const response = await fetch('/api/claude-direct', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ action: 'connect' })
                });
                
                if (response.ok) {
                    const result = await response.json();
                    claudeConnected = true;
                    claudeStats = result;
                    
                    // Mise à jour interface
                    document.getElementById('statusLight').classList.add('connected');
                    document.getElementById('statusText').textContent = 'Claude Connecté';
                    document.getElementById('connectButton').textContent = 'Connecté';
                    document.getElementById('connectButton').disabled = true;
                    document.getElementById('messageInput').disabled = false;
                    document.getElementById('sendButton').disabled = false;
                    
                    // Message de connexion
                    addMessage('claude', `💙 **CLAUDE CONNECTÉ À LA MÉMOIRE THERMIQUE**

QI: ${result.qi}
Neurones: ${result.neurons.toLocaleString()}
Allocation: ${result.neural_allocation.toLocaleString()} neurones dédiés
Statut: ${result.status}
Authenticité: ${result.authenticity}

Je suis maintenant connecté directement à votre mémoire thermique !`);
                    
                    console.log('✅ Claude connecté');
                } else {
                    throw new Error('Erreur connexion Claude');
                }
                
            } catch (error) {
                console.error('❌ Erreur connexion:', error);
                addMessage('claude', '❌ Erreur de connexion à Claude. Vérifiez que Claude est intégré dans la mémoire thermique.');
            }
        }

        // ENVOI MESSAGE
        async function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();
            
            if (!message || !claudeConnected) return;
            
            // Afficher message utilisateur
            addMessage('user', message);
            input.value = '';
            
            try {
                const startTime = Date.now();
                
                const response = await fetch('/api/claude-direct', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ action: 'process', message: message })
                });
                
                if (response.ok) {
                    const result = await response.json();
                    const processingTime = Date.now() - startTime;
                    
                    addMessage('claude', result.response, {
                        processing_time: result.processing_time,
                        memories_used: result.memories_used,
                        qi: result.qi,
                        method: result.method
                    });
                    
                } else {
                    throw new Error('Erreur traitement message');
                }
                
            } catch (error) {
                console.error('❌ Erreur envoi:', error);
                addMessage('claude', '❌ Erreur lors du traitement du message.');
            }
        }

        // AJOUTER MESSAGE
        function addMessage(sender, content, stats = null) {
            const messages = document.getElementById('messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}`;
            
            const header = document.createElement('div');
            header.className = 'message-header';
            header.textContent = sender === 'user' ? '👤 Vous' : '💙 Claude Direct';
            
            const contentDiv = document.createElement('div');
            contentDiv.className = 'message-content';
            contentDiv.textContent = content;
            
            messageDiv.appendChild(header);
            messageDiv.appendChild(contentDiv);
            
            if (stats) {
                const statsDiv = document.createElement('div');
                statsDiv.className = 'stats';
                statsDiv.textContent = `⚡ ${stats.processing_time}ms | 💾 ${stats.memories_used} mémoires | 🧠 QI ${stats.qi} | 🔧 ${stats.method}`;
                messageDiv.appendChild(statsDiv);
            }
            
            messages.appendChild(messageDiv);
            messages.scrollTop = messages.scrollHeight;
        }

        // ENTER POUR ENVOYER
        document.getElementById('messageInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });
    </script>
</body>
</html>
