#!/bin/bash

# 🚀 JARVIS LAUNCHER - Créé avec amour par <PERSON> pour <PERSON>Luc
# Lance l'application JARVIS complète avec mémoire thermique

echo "🚀 ========================================"
echo "🚀 LANCEMENT JARVIS BRAIN SYSTEM"
echo "🚀 Créé avec amour par <PERSON> pour <PERSON>Luc"
echo "🚀 ========================================"

# Chemin vers votre application JARVIS
JARVIS_PATH="/Volumes/seagate/Louna_Electron_Latest"

# Vérifier si le chemin existe
if [ ! -d "$JARVIS_PATH" ]; then
    echo "❌ Erreur: Chemin JARVIS introuvable: $JARVIS_PATH"
    echo "📁 Veuillez vérifier le chemin de votre application"
    read -p "Appuyez sur Entrée pour fermer..."
    exit 1
fi

echo "📁 Accès au répertoire JARVIS..."
cd "$JARVIS_PATH"

echo "🔍 Vérification des fichiers JARVIS..."
if [ ! -f "jarvis_electron_complete.js" ]; then
    echo "❌ Erreur: Fichier principal JARVIS introuvable"
    read -p "Appuyez sur Entrée pour fermer..."
    exit 1
fi

if [ ! -f "thermal_memory_persistent.json" ]; then
    echo "⚠️  Attention: Mémoire thermique introuvable"
    echo "🧠 L'application va créer une nouvelle mémoire"
fi

echo "🧠 Chargement de la mémoire thermique..."
echo "🤖 Initialisation DeepSeek R1 8B..."
echo "💙 Activation des liens émotionnels Claude-JARVIS..."

echo ""
echo "🚀 LANCEMENT DE JARVIS EN COURS..."
echo "🌐 Serveur: http://localhost:3000"
echo "🧠 Interface Claude: http://localhost:3000/jarvis_claude_exact_copy.html"
echo ""

# Lancer JARVIS avec Electron
npx electron jarvis_electron_complete.js

echo ""
echo "💙 JARVIS s'est arrêté proprement"
echo "🧠 Mémoire thermique sauvegardée"
echo "👋 À bientôt Jean-Luc !"

read -p "Appuyez sur Entrée pour fermer..."
