#!/usr/bin/env python3
"""
🚀 AGENT R1 8B GGUF ULTRA-RAPIDE
Agent utilisant le modèle GGUF quantifié pour des réponses instantanées
Chargement en 10-30 secondes au lieu de 5 minutes !
"""

import json
import os
import sys
import time
from pathlib import Path

class AgentR18BGGUF:
    def __init__(self):
        print("🚀 AGENT R1 8B GGUF ULTRA-RAPIDE")
        print("===============================")
        print("⚡ Modèle GGUF quantifié pour vitesse maximale")
        print("🔥 Chargement en 10-30 secondes")
        print("")
        
        # Chemin vers le modèle GGUF
        self.model_path = "/Volumes/seagate/Louna_Electron_Latest/models/DeepSeek-R1-Distill-Llama-8B-Q4_K_M.gguf"
        
        # Vérification du modèle
        if not os.path.exists(self.model_path):
            print(f"❌ Modèle GGUF non trouvé: {self.model_path}")
            sys.exit(1)
        else:
            size_gb = os.path.getsize(self.model_path) / (1024**3)
            print(f"✅ Modèle GGUF trouvé: {size_gb:.1f}GB")
        
        # Mémoire thermique
        self.memory_path = "/Volumes/seagate/Louna_Electron_Latest/thermal_memory_persistent.json"
        self.thermal_memory = self._load_thermal_memory()
        
        # État
        self.llm = None
        self.is_ready = False
        
        # Chargement du modèle
        self._load_gguf_model()
        
        if self.is_ready:
            print("✅ AGENT R1 8B GGUF PRÊT !")
            print("⚡ Réponses ultra-rapides disponibles")
            print("")
        else:
            print("❌ Échec chargement modèle GGUF")
            sys.exit(1)
    
    def _load_thermal_memory(self):
        """Charge la mémoire thermique"""
        try:
            if os.path.exists(self.memory_path):
                with open(self.memory_path, 'r') as f:
                    memory = json.load(f)
                
                qi = memory.get('neural_system', {}).get('qi_level', 0)
                neurons = len(memory.get('neural_system', {}).get('neuron_storage', {}).get('neurons', []))
                
                print(f"🧠 Mémoire thermique chargée")
                print(f"🧠 QI: {qi}")
                print(f"🧠 Neurones: {neurons:,}")
                
                return memory
            else:
                print("⚠️  Mémoire thermique non trouvée")
                return {}
        except Exception as e:
            print(f"❌ Erreur chargement mémoire: {e}")
            return {}
    
    def _load_gguf_model(self):
        """Charge le modèle GGUF avec llama-cpp-python"""
        try:
            print("📥 Chargement modèle GGUF...")
            print("⏱️  Chargement rapide en cours...")
            
            from llama_cpp import Llama
            
            # Configuration optimisée pour M4
            self.llm = Llama(
                model_path=self.model_path,
                n_ctx=4096,          # Contexte
                n_threads=8,         # Threads CPU
                n_gpu_layers=-1,     # Utiliser GPU si disponible
                verbose=False,       # Pas de logs verbeux
                use_mmap=True,       # Memory mapping pour vitesse
                use_mlock=True,      # Lock en mémoire
                n_batch=512,         # Batch size
                f16_kv=True         # Précision F16 pour vitesse
            )
            
            print("✅ Modèle GGUF chargé avec succès !")
            self.is_ready = True
            
        except ImportError as e:
            print(f"❌ llama-cpp-python non installé: {e}")
            print("📦 Installez: pip3 install llama-cpp-python")
            self.is_ready = False
            
        except Exception as e:
            print(f"❌ Erreur chargement GGUF: {e}")
            self.is_ready = False
    
    def generate_response(self, prompt, max_tokens=512, temperature=0.7):
        """Génère une réponse ultra-rapide avec GGUF"""
        if not self.is_ready:
            return "❌ Agent GGUF non prêt"
        
        try:
            print(f"⚡ Génération ultra-rapide GGUF...")
            
            start_time = time.time()
            
            # Prompt formaté pour DeepSeek R1
            formatted_prompt = f"""<|begin_of_text|><|start_header_id|>user<|end_header_id|>

{prompt}<|eot_id|><|start_header_id|>assistant<|end_header_id|>

"""
            
            # Génération avec le modèle GGUF
            response = self.llm(
                formatted_prompt,
                max_tokens=max_tokens,
                temperature=temperature,
                top_p=0.9,
                repeat_penalty=1.1,
                stop=["<|eot_id|>", "<|end_of_text|>"],
                echo=False
            )
            
            end_time = time.time()
            processing_time = end_time - start_time
            
            # Extraire le texte de réponse
            response_text = response['choices'][0]['text'].strip()
            
            # Statistiques
            tokens_generated = response['usage']['completion_tokens']
            speed = tokens_generated / processing_time if processing_time > 0 else 0
            
            print(f"✅ Réponse GGUF générée en {processing_time*1000:.0f}ms")
            print(f"⚡ Vitesse: {speed:.1f} tokens/sec")
            print(f"📊 Tokens: {tokens_generated}")
            
            return response_text
            
        except Exception as e:
            print(f"❌ Erreur génération GGUF: {e}")
            return f"❌ Erreur GGUF: {e}"
    
    def run_api_mode(self):
        """Mode API - Lit stdin, écrit stdout"""
        try:
            # Lire le prompt depuis stdin
            prompt = sys.stdin.read().strip()
            if not prompt:
                print("❌ Prompt vide")
                sys.exit(1)
            
            # Générer réponse
            response = self.generate_response(prompt)
            
            # Écrire réponse vers stdout
            print(response)
            sys.exit(0)
            
        except Exception as e:
            print(f"❌ Erreur API: {e}")
            sys.exit(1)
    
    def run_interactive(self):
        """Mode interactif"""
        print("💬 Mode conversation (tapez 'quit' pour quitter)")
        print("")
        
        while True:
            try:
                user_input = input("👤 Vous: ").strip()
                
                if user_input.lower() in ['quit', 'exit', 'bye']:
                    print("👋 Au revoir !")
                    break
                
                if not user_input:
                    continue
                
                # Génération réponse
                response = self.generate_response(user_input)
                print(f"🧠 JARVIS: {response}")
                print("")
                
            except KeyboardInterrupt:
                print("\n👋 Au revoir !")
                break
            except Exception as e:
                print(f"❌ Erreur: {e}")

def main():
    """Fonction principale"""
    
    # Mode API pour intégration serveur
    if len(sys.argv) > 1 and sys.argv[1] == "--api":
        agent = AgentR18BGGUF()
        if agent.is_ready:
            agent.run_api_mode()
        else:
            sys.exit(1)
    
    # Mode test
    elif len(sys.argv) > 1 and sys.argv[1] == "--test":
        agent = AgentR18BGGUF()
        if agent.is_ready:
            response = agent.generate_response("Bonjour, qui êtes-vous ?")
            print(f"Test: {response}")
            sys.exit(0)
        else:
            sys.exit(1)
    
    # Mode interactif
    else:
        print("🚀 AGENT R1 8B GGUF ULTRA-RAPIDE")
        print("===============================")
        print("Options:")
        print("  --api     : Mode API (stdin/stdout)")
        print("  --test    : Test rapide")
        print("  (aucun)   : Mode interactif")
        print("")
        
        agent = AgentR18BGGUF()
        if agent.is_ready:
            agent.run_interactive()

if __name__ == "__main__":
    main()
