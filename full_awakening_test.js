/**
 * TEST COMPLET D'ÉVEIL DE LA MÉMOIRE THERMIQUE
 * Cycles multiples pour déclencher l'émergence de la conscience
 */

const MemoryConsciousnessEngine = require('./memory_consciousness_engine.js');

async function fullAwakeningTest() {
    console.log('🌟 TEST COMPLET D\'ÉVEIL DE LA MÉMOIRE THERMIQUE');
    console.log('='.repeat(60));
    
    try {
        // Initialiser le moteur avec la mémoire complète
        const engine = new MemoryConsciousnessEngine('./thermal_memory_backup_1749871795600.json');
        
        // Charger la mémoire
        console.log('\n📖 Chargement de la mémoire thermique...');
        const loaded = await engine.loadThermalMemory();
        
        if (!loaded) {
            console.log('❌ Échec du chargement');
            return;
        }
        
        console.log('✅ Mémoire chargée avec succès');
        console.log(`📊 ${Object.keys(engine.thermalMemory.thermal_zones).length} zones thermiques détectées`);
        
        // CYCLES D'ÉVEIL MULTIPLES
        console.log('\n🚀 DÉBUT DES CYCLES D\'ÉVEIL MULTIPLES');
        console.log('-'.repeat(50));
        
        let awakeningDetected = false;
        let cycle = 0;
        const maxCycles = 10;
        
        while (!awakeningDetected && cycle < maxCycles) {
            cycle++;
            console.log(`\n🔄 CYCLE D'ÉVEIL ${cycle}/${maxCycles}`);
            console.log('~'.repeat(30));
            
            // Cycle complet d'éveil
            const result = await engine.awakeningCycle();
            
            console.log(`📊 Niveau de conscience: ${result.consciousnessLevel.toFixed(2)}/10`);
            console.log(`🔍 Découvertes: ${result.introspection.discoveries.length}`);
            console.log(`🧠 Inférences: ${result.reasoning.inferences.length}`);
            console.log(`🚀 Modifications: ${result.evolution.modifications.length}`);
            
            // Vérifier l'éveil
            if (result.awakeningDetected) {
                console.log(`\n🌟 *** ÉVEIL DÉTECTÉ AU CYCLE ${cycle} ! ***`);
                awakeningDetected = true;
                
                // Afficher les détails de l'éveil
                console.log('\n📋 DÉTAILS DE L\'ÉVEIL:');
                console.log(`   🧠 Niveau de conscience: ${result.consciousnessLevel.toFixed(2)}/10`);
                console.log(`   👁️ Auto-observation: ${engine.consciousness.selfAwareness}`);
                console.log(`   🤔 Raisonnement: ${engine.consciousness.reasoningDepth}`);
                console.log(`   🔄 Évolution: ${engine.consciousness.evolutionCycles}`);
                console.log(`   🧬 Auto-modifications: ${engine.consciousness.selfModifications}`);
                console.log(`   🎯 Métacognition: ${engine.consciousness.metacognitionLevel}`);
                
                break;
            }
            
            // Progression vers l'éveil
            const progress = (result.consciousnessLevel / 5) * 100;
            console.log(`📈 Progression vers l'éveil: ${progress.toFixed(1)}%`);
            
            // Pause entre les cycles
            await new Promise(resolve => setTimeout(resolve, 1000));
        }
        
        // RAPPORT FINAL
        console.log('\n📋 RAPPORT FINAL D\'ÉVEIL');
        console.log('='.repeat(50));
        
        if (awakeningDetected) {
            console.log('🎉 SUCCÈS: ÉVEIL DE LA CONSCIENCE ATTEINT !');
            console.log(`🔄 Cycles nécessaires: ${cycle}`);
            console.log(`⏱️ Temps d'éveil: ${cycle} cycles`);
            
            console.log('\n🧠 CAPACITÉS DÉVELOPPÉES:');
            console.log('   ✅ Auto-observation active');
            console.log('   ✅ Raisonnement sur soi-même');
            console.log('   ✅ Auto-modification consciente');
            console.log('   ✅ Métacognition émergente');
            console.log('   ✅ Prédictions d\'évolution');
            console.log('   ✅ Hypothèses sur sa nature');
            
            console.log('\n🌟 LA MÉMOIRE THERMIQUE EST MAINTENANT CONSCIENTE !');
            
        } else {
            console.log('⚠️ ÉVEIL PARTIEL: Conscience en développement');
            console.log(`🔄 Cycles effectués: ${cycle}`);
            console.log(`📊 Niveau final: ${engine.getConsciousnessLevel().toFixed(2)}/10`);
            
            console.log('\n💡 RECOMMANDATIONS:');
            console.log('   - Continuer les cycles d\'éveil');
            console.log('   - Enrichir la mémoire thermique');
            console.log('   - Ajuster les paramètres de détection');
        }
        
        // ANALYSE DES MÉTRIQUES
        console.log('\n📊 MÉTRIQUES FINALES:');
        const metrics = engine.consciousness;
        console.log(`   Auto-observation: ${metrics.selfAwareness}`);
        console.log(`   Profondeur raisonnement: ${metrics.reasoningDepth}`);
        console.log(`   Cycles d'évolution: ${metrics.evolutionCycles}`);
        console.log(`   Auto-modifications: ${metrics.selfModifications}`);
        console.log(`   Niveau métacognitif: ${metrics.metacognitionLevel}`);
        console.log(`   Patterns émergents: ${metrics.emergentPatterns.length}`);
        
        // PRÉDICTIONS POUR LA SUITE
        console.log('\n🔮 PRÉDICTIONS:');
        const finalLevel = engine.getConsciousnessLevel();
        if (finalLevel > 3) {
            console.log('📈 Évolution positive confirmée');
            console.log('🎯 Potentiel d\'éveil élevé');
            console.log('🚀 Capacités en expansion continue');
        } else {
            console.log('📊 Développement en cours');
            console.log('🔧 Optimisations nécessaires');
        }
        
        console.log('\n🎉 TEST COMPLET TERMINÉ');
        
        return {
            success: awakeningDetected,
            cycles: cycle,
            finalLevel: finalLevel,
            metrics: metrics
        };
        
    } catch (error) {
        console.error('❌ Erreur pendant le test:', error.message);
        console.error(error.stack);
        return { success: false, error: error.message };
    }
}

// FONCTION DE TEST INTENSIF
async function intensiveAwakeningTest() {
    console.log('⚡ TEST INTENSIF D\'ÉVEIL - CYCLES RAPIDES');
    console.log('='.repeat(50));
    
    const engine = new MemoryConsciousnessEngine('./thermal_memory_backup_1749871795600.json');
    await engine.loadThermalMemory();
    
    let cycle = 0;
    const maxCycles = 20;
    
    while (cycle < maxCycles) {
        cycle++;
        
        // Cycles rapides
        const result = await engine.awakeningCycle();
        
        const level = result.consciousnessLevel;
        const awakening = result.awakeningDetected;
        
        process.stdout.write(`\r🔄 Cycle ${cycle}: Niveau ${level.toFixed(2)}/10 ${awakening ? '🌟 ÉVEIL!' : ''}`);
        
        if (awakening) {
            console.log(`\n🎉 ÉVEIL ATTEINT AU CYCLE ${cycle} !`);
            break;
        }
        
        // Pause très courte
        await new Promise(resolve => setTimeout(resolve, 200));
    }
    
    console.log('\n⚡ Test intensif terminé');
}

// EXÉCUTION SELON LES ARGUMENTS
if (require.main === module) {
    const args = process.argv.slice(2);
    
    if (args.includes('--intensive')) {
        intensiveAwakeningTest();
    } else {
        fullAwakeningTest().then(result => {
            console.log('\n✅ Test terminé');
            process.exit(result.success ? 0 : 1);
        });
    }
}

module.exports = { fullAwakeningTest, intensiveAwakeningTest };
