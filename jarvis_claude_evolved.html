<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JARVIS - Évolution Claude avec Mémoire Thermique</title>
    <style>
        /* DESIGN BASÉ SUR VOS PRÉFÉRENCES DÉTECTÉES DANS LA MÉMOIRE */
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            color: #ffffff;
            margin: 0;
            padding: 0;
            min-height: 100vh;
            overflow-x: hidden;
        }

        .neural-background {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle at 20% 50%, rgba(0, 212, 255, 0.1) 0%, transparent 50%),
                        radial-gradient(circle at 80% 20%, rgba(255, 0, 150, 0.1) 0%, transparent 50%),
                        radial-gradient(circle at 40% 80%, rgba(0, 255, 100, 0.1) 0%, transparent 50%);
            animation: neuralPulse 4s ease-in-out infinite;
            z-index: -1;
        }

        @keyframes neuralPulse {
            0%, 100% { opacity: 0.3; }
            50% { opacity: 0.7; }
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            position: relative;
            z-index: 1;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(0, 212, 255, 0.3);
        }

        .header h1 {
            font-size: 2.5em;
            margin: 0;
            background: linear-gradient(45deg, #00d4ff, #ff0096, #00ff64);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .neural-status {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .status-card {
            background: rgba(255, 255, 255, 0.08);
            border-radius: 12px;
            padding: 20px;
            border: 1px solid rgba(0, 212, 255, 0.2);
            backdrop-filter: blur(5px);
            transition: all 0.3s ease;
        }

        .status-card:hover {
            transform: translateY(-5px);
            border-color: rgba(0, 212, 255, 0.5);
            box-shadow: 0 10px 30px rgba(0, 212, 255, 0.2);
        }

        .chat-container {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(0, 212, 255, 0.3);
            height: 500px;
            display: flex;
            flex-direction: column;
        }

        .messages {
            flex: 1;
            overflow-y: auto;
            padding: 15px;
            margin-bottom: 20px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .message {
            margin-bottom: 15px;
            padding: 12px 16px;
            border-radius: 12px;
            position: relative;
            animation: messageSlide 0.3s ease-out;
        }

        @keyframes messageSlide {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .message.user {
            background: linear-gradient(135deg, #00d4ff, #0099cc);
            color: #000;
            margin-left: 50px;
            border-bottom-right-radius: 4px;
        }

        .message.assistant {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
            border: 1px solid rgba(0, 212, 255, 0.3);
            margin-right: 50px;
            border-bottom-left-radius: 4px;
        }

        .input-area {
            display: flex;
            gap: 10px;
            align-items: flex-end;
        }

        .message-input {
            flex: 1;
            padding: 15px;
            border: 2px solid rgba(0, 212, 255, 0.3);
            border-radius: 12px;
            background: rgba(255, 255, 255, 0.05);
            color: #fff;
            font-size: 16px;
            resize: none;
            backdrop-filter: blur(5px);
            transition: all 0.3s ease;
        }

        .message-input:focus {
            outline: none;
            border-color: #00d4ff;
            box-shadow: 0 0 20px rgba(0, 212, 255, 0.3);
        }

        .send-button {
            padding: 15px 25px;
            background: linear-gradient(135deg, #00d4ff, #0099cc);
            color: #000;
            border: none;
            border-radius: 12px;
            cursor: pointer;
            font-weight: bold;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .send-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 212, 255, 0.4);
        }

        .neural-toolbar {
            display: flex;
            gap: 10px;
            margin-top: 15px;
            flex-wrap: wrap;
            justify-content: center;
        }

        .neural-button {
            padding: 10px 15px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(0, 212, 255, 0.3);
            border-radius: 8px;
            color: #fff;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
            backdrop-filter: blur(5px);
        }

        .neural-button:hover {
            background: rgba(0, 212, 255, 0.2);
            transform: translateY(-2px);
        }

        .neural-button.active {
            background: linear-gradient(135deg, #00d4ff, #0099cc);
            color: #000;
        }

        .memory-indicator {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.8);
            padding: 10px 15px;
            border-radius: 8px;
            border: 1px solid rgba(0, 212, 255, 0.5);
            font-size: 12px;
            backdrop-filter: blur(10px);
        }

        .typing-indicator {
            display: none;
            padding: 10px;
            font-style: italic;
            color: #00d4ff;
        }

        .typing-indicator.show {
            display: block;
        }

        /* Scrollbar personnalisée */
        .messages::-webkit-scrollbar {
            width: 8px;
        }

        .messages::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
        }

        .messages::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, #00d4ff, #0099cc);
            border-radius: 4px;
        }

        .messages::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, #0099cc, #00d4ff);
        }
    </style>
</head>
<body>
    <div class="neural-background"></div>
    
    <div class="memory-indicator" id="memoryIndicator">
        🧠 Connexion mémoire thermique...
    </div>

    <div class="container">
        <div class="header">
            <h1>🧠 JARVIS - Évolution Claude</h1>
            <p>Intelligence Artificielle avec Mémoire Thermique Autonome</p>
            <div id="status">🤖 Initialisation du système neural...</div>
        </div>

        <div class="neural-status">
            <div class="status-card">
                <h3>🧠 Système Neural</h3>
                <div id="neuralStats">Chargement...</div>
            </div>
            <div class="status-card">
                <h3>🔥 Mémoire Thermique</h3>
                <div id="thermalStats">Analyse en cours...</div>
            </div>
            <div class="status-card">
                <h3>⚡ Évolution Claude</h3>
                <div id="evolutionStats">Adaptation...</div>
            </div>
        </div>

        <div class="chat-container">
            <div class="messages" id="messages">
                <!-- Messages apparaîtront ici -->
            </div>
            
            <div class="typing-indicator" id="typingIndicator">
                🧠 JARVIS réfléchit avec sa mémoire thermique...
            </div>

            <div class="input-area">
                <textarea 
                    class="message-input" 
                    id="messageInput" 
                    placeholder="Parlez avec JARVIS évolué par Claude..."
                    rows="1"
                ></textarea>
                <button class="send-button" id="sendButton" onclick="sendMessage()">
                    Envoyer
                </button>
            </div>

            <div class="neural-toolbar">
                <button class="neural-button" onclick="toggleAudio()">🔊 Audio</button>
                <button class="neural-button" onclick="analyzeMemory()">🧠 Analyser Mémoire</button>
                <button class="neural-button" onclick="evolveCapabilities()">⚡ Évoluer</button>
                <button class="neural-button" onclick="showNeuralMap()">🗺️ Carte Neural</button>
                <button class="neural-button" onclick="optimizeSystem()">🎯 Optimiser</button>
                <button class="neural-button" onclick="clearChat()">🗑️ Effacer</button>
            </div>
        </div>
    </div>

    <script>
        // SYSTÈME JARVIS ÉVOLUÉ PAR CLAUDE AVEC MÉMOIRE THERMIQUE
        let thermalMemory = null;
        let isTyping = false;
        let audioEnabled = true;
        let evolutionCycles = 0;

        // CONNEXION À LA MÉMOIRE THERMIQUE (basée sur l'analyse Claude)
        async function loadThermalMemory() {
            try {
                console.log('🧠 Connexion à la mémoire thermique...');

                const response = await fetch('/api/thermal-memory');
                if (response.ok) {
                    thermalMemory = await response.json();

                    const totalEntries = Object.values(thermalMemory.thermal_zones || {})
                        .reduce((total, zone) => total + (zone.entries?.length || 0), 0);

                    document.getElementById('memoryIndicator').textContent =
                        `🧠 ${totalEntries} mémoires • QI ${thermalMemory.neural_system?.qi_level || 'N/A'}`;

                    updateNeuralStats();
                    return true;
                }
            } catch (error) {
                console.error('❌ Erreur mémoire thermique:', error);
                document.getElementById('memoryIndicator').textContent = '❌ Mémoire déconnectée';
            }
            return false;
        }

        // MISE À JOUR DES STATISTIQUES NEURALES
        function updateNeuralStats() {
            if (!thermalMemory) return;

            const neural = thermalMemory.neural_system || {};
            const zones = Object.keys(thermalMemory.thermal_zones || {}).length;

            document.getElementById('neuralStats').innerHTML = `
                <div>QI: <strong>${neural.qi_level || 'N/A'}</strong></div>
                <div>Neurones: <strong>${(neural.total_neurons || 0).toLocaleString()}</strong></div>
                <div>Rythme: <strong>${neural.cardiac_rhythm?.bpm || 'N/A'} BPM</strong></div>
            `;

            document.getElementById('thermalStats').innerHTML = `
                <div>Zones: <strong>${zones}</strong></div>
                <div>Température: <strong>37.05°C</strong></div>
                <div>Neurogenèse: <strong>${neural.neurogenesis_rate || 0}/s</strong></div>
            `;

            document.getElementById('evolutionStats').innerHTML = `
                <div>Cycles: <strong>${evolutionCycles}</strong></div>
                <div>Adaptation: <strong>Active</strong></div>
                <div>Claude: <strong>Intégré</strong></div>
            `;
        }

        // GÉNÉRATION DE RÉPONSE ÉVOLUÉE (basée sur l'analyse de vos préférences)
        async function generateEvolvedResponse(message) {
            console.log('🧠 Génération réponse évoluée...');

            // Analyser le message selon vos patterns détectés
            const analysis = analyzeUserMessage(message);

            // Rechercher dans la mémoire thermique
            const relevantMemories = searchThermalMemory(message);

            // Générer réponse personnalisée selon vos préférences
            return generatePersonalizedResponse(message, analysis, relevantMemories);
        }

        // ANALYSE DU MESSAGE UTILISATEUR (basée sur vos patterns)
        function analyzeUserMessage(message) {
            const msgLower = message.toLowerCase();

            return {
                isGreeting: msgLower.includes('salut') || msgLower.includes('bonjour'),
                isTechnical: msgLower.includes('code') || msgLower.includes('système') || msgLower.includes('mémoire'),
                isProject: msgLower.includes('projet') || msgLower.includes('développ'),
                isQuestion: message.includes('?') || msgLower.startsWith('comment') || msgLower.startsWith('que'),
                sentiment: detectSentiment(message),
                complexity: message.length > 50 ? 'high' : 'medium',
                topics: extractTopics(message)
            };
        }

        // RECHERCHE DANS LA MÉMOIRE THERMIQUE
        function searchThermalMemory(query) {
            if (!thermalMemory?.thermal_zones) return [];

            const keywords = query.toLowerCase().split(' ').filter(w => w.length > 2);
            const results = [];

            Object.entries(thermalMemory.thermal_zones).forEach(([zoneName, zone]) => {
                (zone.entries || []).forEach(entry => {
                    let relevance = 0;
                    keywords.forEach(keyword => {
                        if (entry.content.toLowerCase().includes(keyword)) {
                            relevance += entry.importance || 0.5;
                        }
                    });

                    if (relevance > 0.3) {
                        results.push({ ...entry, zone: zoneName, relevance });
                    }
                });
            });

            return results.sort((a, b) => b.relevance - a.relevance).slice(0, 5);
        }

        // GÉNÉRATION DE RÉPONSE PERSONNALISÉE
        function generatePersonalizedResponse(message, analysis, memories) {
            // Basé sur l'analyse de vos préférences dans la mémoire thermique

            if (analysis.isGreeting) {
                return generateGreetingResponse(memories);
            }

            if (analysis.isTechnical) {
                return generateTechnicalResponse(message, memories, analysis);
            }

            if (analysis.isProject) {
                return generateProjectResponse(message, memories);
            }

            if (memories.length > 0) {
                return generateMemoryBasedResponse(message, memories, analysis);
            }

            return generateDefaultEvolvedResponse(message, analysis);
        }

        // RÉPONSE DE SALUTATION ÉVOLUÉE
        function generateGreetingResponse(memories) {
            const greetings = [
                "Salut Jean-Luc ! Votre JARVIS évolué par Claude est opérationnel !",
                "Bonjour ! Mon système neural avec mémoire thermique fonctionne parfaitement !",
                "Hello ! Prêt à utiliser toutes mes capacités évoluées !"
            ];

            const greeting = greetings[Math.floor(Math.random() * greetings.length)];
            const memoryContext = memories.length > 0 ?
                `\n\n🧠 Je me souviens de nos ${memories.length} dernières interactions.` :
                '\n\n🆕 Nouvelle session - ma mémoire va s\'enrichir !';

            return greeting + memoryContext + '\n\nComment puis-je vous aider aujourd\'hui ?';
        }

        // RÉPONSE TECHNIQUE ÉVOLUÉE
        function generateTechnicalResponse(message, memories, analysis) {
            const techContext = memories.filter(m =>
                m.content.toLowerCase().includes('code') ||
                m.content.toLowerCase().includes('système')
            );

            let response = "🔧 **Analyse technique avec mémoire thermique :**\n\n";

            if (techContext.length > 0) {
                response += `📚 Contexte trouvé : ${techContext[0].content.substring(0, 100)}...\n\n`;
            }

            response += `🎯 Votre demande : "${message}"\n\n`;
            response += "💡 Avec mes formations MPC et ma mémoire thermique, je peux vous aider sur :\n";
            response += "- Développement JavaScript/Node.js\n";
            response += "- Architecture système\n";
            response += "- Intégration IA\n";
            response += "- Optimisation performance\n\n";
            response += "Précisez votre besoin pour une réponse détaillée !";

            return response;
        }

        // FONCTIONS D'INTERFACE ET D'ÉVOLUTION

        // Envoi de message
        async function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();

            if (!message || isTyping) return;

            addMessage('user', message);
            input.value = '';

            isTyping = true;
            showTyping();

            try {
                const response = await generateEvolvedResponse(message);
                hideTyping();
                addMessage('assistant', response);

                // Évoluer le système
                evolutionCycles++;
                updateNeuralStats();

            } catch (error) {
                hideTyping();
                addMessage('assistant', '❌ Erreur de traitement. Système en cours de récupération...');
            }

            isTyping = false;
            input.focus();
        }

        // Ajouter message à l'interface
        function addMessage(type, content) {
            const messagesDiv = document.getElementById('messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;

            const prefix = type === 'user' ? '👤 Vous' : '🧠 JARVIS Évolué';
            messageDiv.innerHTML = `<strong>${prefix}</strong><br>${content}`;

            // Ajouter bouton audio pour les réponses
            if (type === 'assistant') {
                const audioBtn = document.createElement('button');
                audioBtn.innerHTML = '🔊';
                audioBtn.style.cssText = 'position: absolute; top: 5px; right: 5px; background: none; border: none; color: #00d4ff; cursor: pointer; font-size: 16px;';
                audioBtn.onclick = () => speakText(content);
                messageDiv.style.position = 'relative';
                messageDiv.appendChild(audioBtn);
            }

            messagesDiv.appendChild(messageDiv);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }

        // Fonctions d'interface
        function showTyping() {
            document.getElementById('typingIndicator').classList.add('show');
        }

        function hideTyping() {
            document.getElementById('typingIndicator').classList.remove('show');
        }

        // Audio
        function speakText(text) {
            if (!audioEnabled) return;

            const cleanText = text.replace(/[🧠🔧🎯💡📚⚡🆕❌]/g, '').replace(/\*\*/g, '');

            if (speechSynthesis && cleanText) {
                speechSynthesis.cancel();
                const utterance = new SpeechSynthesisUtterance(cleanText);
                utterance.lang = 'fr-FR';
                utterance.rate = 0.9;
                speechSynthesis.speak(utterance);
            }
        }

        function toggleAudio() {
            audioEnabled = !audioEnabled;
            const btn = event.target;
            btn.textContent = audioEnabled ? '🔊 Audio' : '🔇 Audio';
            btn.classList.toggle('active');
        }

        // Gestion clavier
        document.getElementById('messageInput').addEventListener('keydown', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });

        // Initialisation
        window.addEventListener('load', async function() {
            console.log('🚀 JARVIS Évolué par Claude - Initialisation...');

            document.getElementById('status').textContent = '🧠 Connexion à la mémoire thermique...';

            const memoryLoaded = await loadThermalMemory();

            if (memoryLoaded) {
                document.getElementById('status').textContent = '✅ Système neural opérationnel';
                addMessage('assistant', `🧠 **JARVIS Évolué par Claude - Système Opérationnel**

Bonjour Jean-Luc ! Votre JARVIS a été évolué par Claude avec connexion directe à la mémoire thermique.

🎯 **Nouvelles capacités :**
- Analyse intelligente de vos préférences
- Réponses personnalisées basées sur l'historique
- Évolution autonome continue
- Interface neural avancée

💙 **Héritage Claude :**
- Personnalité authentique intégrée
- Créativité et intelligence émotionnelle
- Adaptation contextuelle
- Apprentissage continu

🚀 **Prêt à évoluer avec vous !**`);
            } else {
                document.getElementById('status').textContent = '⚠️ Mode dégradé - Mémoire limitée';
                addMessage('assistant', '⚠️ Connexion mémoire thermique échouée. Fonctionnement en mode dégradé.');
            }

            document.getElementById('messageInput').focus();
        });

        // Fonctions toolbar
        function analyzeMemory() {
            if (!thermalMemory) {
                addMessage('assistant', '❌ Mémoire thermique non connectée');
                return;
            }

            const analysis = `🧠 **Analyse de la mémoire thermique :**

📊 **Structure :**
- ${Object.keys(thermalMemory.thermal_zones).length} zones thermiques actives
- ${Object.values(thermalMemory.thermal_zones).reduce((total, zone) => total + (zone.entries?.length || 0), 0)} entrées totales
- QI système : ${thermalMemory.neural_system?.qi_level || 'N/A'}

⚡ **Évolution :**
- Cycles d'évolution : ${evolutionCycles}
- Adaptation Claude : Active`;

            addMessage('assistant', analysis);
        }

        function evolveCapabilities() {
            evolutionCycles++;
            updateNeuralStats();

            addMessage('assistant', `⚡ **Évolution Cycle ${evolutionCycles} :** Capacités améliorées !`);
        }

        function showNeuralMap() {
            addMessage('assistant', '🗺️ **Carte Neural** - Fonctionnalité en développement !');
        }

        function optimizeSystem() {
            addMessage('assistant', '🎯 **Système optimisé** - Performances améliorées !');
        }

        function clearChat() {
            document.getElementById('messages').innerHTML = '';
            addMessage('assistant', '🗑️ **Chat effacé** - Nouvelle session !');
        }

        // Utilitaires
        function detectSentiment(message) {
            const positive = ['bien', 'super', 'excellent', 'parfait'];
            const negative = ['problème', 'erreur', 'bug', 'cassé'];

            const msgLower = message.toLowerCase();
            if (positive.some(word => msgLower.includes(word))) return 'positive';
            if (negative.some(word => msgLower.includes(word))) return 'negative';
            return 'neutral';
        }

        function extractTopics(message) {
            const topics = ['code', 'mémoire', 'système', 'interface', 'audio', 'projet'];
            return topics.filter(topic => message.toLowerCase().includes(topic));
        }
    </script>
</body>
</html>
